'use client';

import { useState, useEffect, useRef } from 'react';
import { useParams } from 'next/navigation';
import { Camera, User, Phone, Mail, Upload, Image as ImageIcon, Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

interface Event {
  id: string;
  name: string;
  description?: string;
  uniqueId: string;
  isPublic: boolean;
  createdAt: string;
  studio: {
    name: string;
    description?: string;
  };
  stats: {
    totalImages: number;
    totalVisitors: number;
  };
}

interface VisitorForm {
  name: string;
  contact?: string;
  email?: string;
}

export default function EventAccessPage() {
  const params = useParams();
  const uniqueId = params.uniqueId as string;
  
  const [event, setEvent] = useState<Event | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [step, setStep] = useState<'info' | 'selfie' | 'results'>('info');
  const [visitorId, setVisitorId] = useState<string | null>(null);
  const [matchedImages, setMatchedImages] = useState<any[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [visitorData, setVisitorData] = useState<VisitorForm | null>(null);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [faceDetected, setFaceDetected] = useState(false);
  const [facePosition, setFacePosition] = useState<{x: number, y: number, width: number, height: number} | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<VisitorForm>();

  useEffect(() => {
    if (uniqueId) {
      loadEvent();
    }
  }, [uniqueId]);

  // Auto-start camera when selfie step is reached
  useEffect(() => {
    if (step === 'selfie' && !stream && videoRef.current) {
      console.log('Selfie step reached, auto-starting camera...');
      startCamera();
    }
  }, [step, stream]);

  // Cleanup camera on unmount
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, []);

  // Face detection loop
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (isCameraReady && videoRef.current) {
      // Run face detection every 500ms
      intervalId = setInterval(() => {
        detectFace();
      }, 500);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isCameraReady]);

  const loadEvent = async () => {
    try {
      setIsLoading(true);

      // Use the new visitor API to get event info
      const response = await fetch(`/api/visitors/event/${uniqueId}/info`);
      const data = await response.json();

      if (data.success) {
        setEvent(data.data);
      } else {
        toast.error('Event not found or not accessible');
      }
    } catch (error) {
      toast.error('Failed to load event');
      console.error('Error loading event:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const startCamera = async () => {
    try {
      console.log('Requesting camera access...');

      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640, min: 320 },
          height: { ideal: 480, min: 240 },
          facingMode: 'user'
        },
        audio: false
      });

      console.log('Camera stream obtained:', mediaStream);
      setStream(mediaStream);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;

        videoRef.current.onloadedmetadata = () => {
          console.log('Video metadata loaded');
          console.log('Video dimensions:', videoRef.current?.videoWidth, 'x', videoRef.current?.videoHeight);

          videoRef.current?.play().then(() => {
            console.log('Video is playing');
            setIsCameraReady(true);
          }).catch(err => {
            console.error('Failed to play video:', err);
            setIsCameraReady(true); // Still allow capture
          });
        };

        videoRef.current.onerror = (err) => {
          console.error('Video error:', err);
          toast.error('Video playback error');
        };
      }
    } catch (error: any) {
      toast.error(`Failed to access camera: ${error.message}`);
      console.error('Camera error:', error);
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => {
        console.log('Stopping track:', track.kind);
        track.stop();
      });
      setStream(null);
      setIsCameraReady(false);
      setFaceDetected(false);
      setFacePosition(null);
    }
  };

  // Simple face detection using video analysis
  const detectFace = () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context || video.videoWidth === 0) return;

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw current frame
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Get image data for basic face detection
    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

    // Simple face detection based on skin tone and face proportions
    // This is a basic implementation - in production you'd use a proper face detection library
    const faceDetected = detectFaceInImageData(imageData);

    setFaceDetected(faceDetected.detected);
    if (faceDetected.detected && faceDetected.position) {
      setFacePosition(faceDetected.position);
    } else {
      setFacePosition(null);
    }
  };

  // Basic face detection algorithm (simplified)
  const detectFaceInImageData = (imageData: ImageData) => {
    const { data, width, height } = imageData;

    // Look for face-like regions in the center area
    const centerX = width / 2;
    const centerY = height / 2;
    const searchRadius = Math.min(width, height) / 4;

    let skinPixels = 0;
    let totalPixels = 0;

    // Sample pixels in center region
    for (let y = centerY - searchRadius; y < centerY + searchRadius; y += 5) {
      for (let x = centerX - searchRadius; x < centerX + searchRadius; x += 5) {
        if (x >= 0 && x < width && y >= 0 && y < height) {
          const index = (y * width + x) * 4;
          const r = data[index];
          const g = data[index + 1];
          const b = data[index + 2];

          // Simple skin tone detection
          if (isSkinTone(r, g, b)) {
            skinPixels++;
          }
          totalPixels++;
        }
      }
    }

    const skinRatio = skinPixels / totalPixels;
    const detected = skinRatio > 0.3; // If 30% of center area is skin tone

    return {
      detected,
      position: detected ? {
        x: centerX - searchRadius,
        y: centerY - searchRadius,
        width: searchRadius * 2,
        height: searchRadius * 2
      } : null
    };
  };

  // Simple skin tone detection
  const isSkinTone = (r: number, g: number, b: number) => {
    // Basic skin tone ranges (simplified)
    return (
      r > 95 && g > 40 && b > 20 &&
      r > g && r > b &&
      Math.abs(r - g) > 15 &&
      Math.max(r, g, b) - Math.min(r, g, b) > 15
    );
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) {
      console.error('Video or canvas ref not available');
      return null;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) {
      console.error('Canvas context not available');
      return null;
    }

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth || 640;
    canvas.height = video.videoHeight || 480;

    console.log('Capturing photo with dimensions:', canvas.width, 'x', canvas.height);

    // Draw the video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    return new Promise<Blob | null>((resolve) => {
      canvas.toBlob(resolve, 'image/jpeg', 0.8);
    });
  };

  const onSubmitVisitorInfo = async (data: VisitorForm) => {
    try {
      setIsProcessing(true);

      // Validate required fields
      if (!data.name?.trim()) {
        toast.error('Name is required');
        return;
      }

      if (!data.contact?.trim()) {
        toast.error('Phone number is required');
        return;
      }

      // Store visitor data for later use
      setVisitorData(data);

      // Move to selfie step first, then start camera
      setStep('selfie');

      // Wait a bit for the video element to mount, then start camera
      setTimeout(async () => {
        await startCamera();
      }, 500);
    } catch (error) {
      toast.error('Failed to start camera');
      console.error('Error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleTakeSelfie = async () => {
    try {
      setIsProcessing(true);
      
      const photoBlob = await capturePhoto();
      if (!photoBlob) {
        toast.error('Failed to capture photo');
        return;
      }

      // Stop camera
      stopCamera();

      // Use FotoOwl-style registration API (one-step process)
      const formData = new FormData();
      formData.append('name', visitorData?.name || '');
      formData.append('phone', visitorData?.contact || '');
      formData.append('email', visitorData?.email || '');
      formData.append('selfie', photoBlob, 'selfie.jpg');

      console.log('Sending FotoOwl-style registration:', {
        name: visitorData?.name,
        phone: visitorData?.contact,
        email: visitorData?.email,
        selfie_size: photoBlob.size
      });

      // Call our new visitor registration API
      const response = await fetch(`/api/visitors/event/${uniqueId}/register`, {
        method: 'POST',
        body: formData,
      });

      let result;
      try {
        result = await response.json();
      } catch (parseError) {
        console.error('Failed to parse response:', parseError);
        toast.error('Invalid response from server');
        return;
      }

      console.log('FotoOwl registration response:', result);

      if (!response.ok) {
        console.error('Registration error:', response.status, result);
        const errorMessage = result?.message || result?.error || `Server error (${response.status})`;
        toast.error(errorMessage);
        return;
      }

      if (result.success) {
        setVisitorId(result.data.visitor.id);
        setMatchedImages(result.data.matches.images || []);
        setStep('results');

        // Show appropriate message based on results
        const matchCount = result.data.matches.total || 0;
        if (matchCount > 0) {
          toast.success(`🎉 Found ${matchCount} photos with your face!`);
        } else {
          toast.success('Registration successful! No matching photos found at this time.');
        }

        // Update gallery stats
        if (result.data.visitor.id && matchCount > 0) {
          try {
            await fetch(`/api/visitors/visitor/${result.data.visitor.id}/gallery-stats`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                imagesViewed: matchCount
              }),
            });
          } catch (statsError) {
            console.error('Failed to update gallery stats:', statsError);
          }
        }
      } else {
        toast.error(result.message || 'Registration failed');
      }
    } catch (error) {
      toast.error('Failed to process registration');
      console.error('Error in FotoOwl registration:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadImage = (imagePath: string, filename: string) => {
    const backendUrl = process.env.NEXT_PUBLIC_API_URL?.replace('/api', '') || 'http://localhost:5000';
    const link = document.createElement('a');
    link.href = `${backendUrl}/${imagePath}`;
    link.download = filename;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Event Not Found</h1>
          <p className="text-gray-600">The event you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        {/* Event Header */}
        <div className="text-center mb-8">
          <div className="mx-auto h-16 w-16 bg-primary-100 rounded-full flex items-center justify-center mb-4">
            <Camera className="h-8 w-8 text-primary-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">{event.name}</h1>
          {event.description && (
            <p className="text-gray-600 mt-2">{event.description}</p>
          )}
          <p className="text-sm text-gray-500 mt-2">
            by {event.studio.name} • {event.stats.totalImages} photos available
          </p>
        </div>

        {/* Step 1: Visitor Information */}
        {step === 'info' && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Enter Your Information</h2>
            
            <form onSubmit={handleSubmit(onSubmitVisitorInfo)} className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Full Name *
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    {...register('name', { required: 'Name is required' })}
                    id="name"
                    type="text"
                    className="input-field pl-10"
                    placeholder="Enter your full name"
                  />
                </div>
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="contact" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    {...register('contact', {
                      required: 'Phone number is required',
                      pattern: {
                        value: /^[\+]?[1-9][\d]{0,15}$/,
                        message: 'Please enter a valid phone number'
                      }
                    })}
                    id="contact"
                    type="tel"
                    className="input-field pl-10"
                    placeholder="Enter your phone number"
                  />
                </div>
                {errors.contact && (
                  <p className="mt-1 text-sm text-red-600">{errors.contact.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    {...register('email', {
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: 'Invalid email address'
                      }
                    })}
                    id="email"
                    type="email"
                    className="input-field pl-10"
                    placeholder="Enter your email address"
                  />
                </div>
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>

              <div className="pt-4">
                <button
                  type="submit"
                  disabled={isProcessing}
                  className="w-full btn-primary flex items-center justify-center gap-2"
                >
                  {isProcessing ? (
                    <Loader2 className="h-5 w-5 animate-spin" />
                  ) : (
                    <Camera className="h-5 w-5" />
                  )}
                  {isProcessing ? 'Starting Camera...' : 'Continue to Face Scan'}
                </button>
              </div>
            </form>

            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>How it works:</strong> After entering your information, you'll take a selfie. 
                Our face recognition system will then find all photos in this event that contain your face.
              </p>
            </div>
          </div>
        )}

        {/* Step 2: Selfie Capture */}
        {step === 'selfie' && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Take Your Selfie</h2>

            {/* Manual Camera Controls */}
            {!stream && (
              <div className="text-center mb-6">
                <button
                  onClick={startCamera}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center gap-2 mx-auto font-medium"
                >
                  <Camera className="h-5 w-5" />
                  Start Camera
                </button>
              </div>
            )}

            <div className="text-center">
              <div className="relative inline-block">
                <video
                  key="selfie-video"
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className="border-2 border-gray-300 rounded-lg max-w-full"
                  style={{
                    width: '640px',
                    height: '480px',
                    backgroundColor: '#f3f4f6',
                    objectFit: 'cover'
                  }}
                  onLoadedMetadata={() => {
                    console.log('Video metadata loaded in JSX');
                    if (videoRef.current) {
                      console.log('Video element ready, dimensions:', videoRef.current.videoWidth, 'x', videoRef.current.videoHeight);
                    }
                  }}
                />

                {/* Face Detection Overlay */}
                {facePosition && (
                  <div
                    className="absolute border-2 border-green-400 rounded"
                    style={{
                      left: `${(facePosition.x / 640) * 100}%`,
                      top: `${(facePosition.y / 480) * 100}%`,
                      width: `${(facePosition.width / 640) * 100}%`,
                      height: `${(facePosition.height / 480) * 100}%`,
                      pointerEvents: 'none'
                    }}
                  >
                    <div className="absolute -top-6 left-0 bg-green-400 text-white text-xs px-2 py-1 rounded">
                      Face Detected
                    </div>
                  </div>
                )}

                {/* Face Detection Status */}
                <div className="absolute top-2 right-2 flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${faceDetected ? 'bg-green-400' : 'bg-red-400'}`}></div>
                  <span className="text-sm font-medium text-white bg-black bg-opacity-50 px-2 py-1 rounded">
                    {faceDetected ? 'Face Detected' : 'No Face'}
                  </span>
                </div>

                <canvas ref={canvasRef} className="hidden" />
              </div>
              
              <div className="mt-6 space-y-4">
                {/* Face Detection Guidance */}
                {isCameraReady && (
                  <div className={`p-4 rounded-lg border ${
                    faceDetected
                      ? 'bg-green-50 border-green-200'
                      : 'bg-yellow-50 border-yellow-200'
                  }`}>
                    <div className="flex items-center gap-2 mb-2">
                      <div className={`w-3 h-3 rounded-full ${faceDetected ? 'bg-green-400' : 'bg-yellow-400'}`}></div>
                      <span className={`font-medium ${
                        faceDetected ? 'text-green-800' : 'text-yellow-800'
                      }`}>
                        {faceDetected ? 'Perfect! Face detected' : 'Position your face in the center'}
                      </span>
                    </div>
                    <p className={`text-sm ${
                      faceDetected ? 'text-green-700' : 'text-yellow-700'
                    }`}>
                      {faceDetected
                        ? 'Your face is clearly visible. You can now take the photo.'
                        : 'Look directly at the camera and make sure your face is in the center of the frame.'
                      }
                    </p>
                  </div>
                )}

                {!isCameraReady ? (
                  <div className="flex items-center justify-center">
                    <Loader2 className="h-6 w-6 animate-spin text-primary-600 mr-2" />
                    <span className="text-gray-600">Starting camera...</span>
                  </div>
                ) : (
                  <button
                    onClick={handleTakeSelfie}
                    disabled={isProcessing || !faceDetected}
                    className={`px-6 py-3 rounded-lg flex items-center gap-2 mx-auto font-medium transition-colors ${
                      faceDetected && !isProcessing
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-gray-400 text-gray-200 cursor-not-allowed'
                    }`}
                  >
                    {isProcessing ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : (
                      <Camera className="h-5 w-5" />
                    )}
                    {isProcessing
                      ? 'Processing...'
                      : faceDetected
                        ? 'Take Photo & Find My Pictures'
                        : 'Position Your Face First'
                    }
                  </button>
                )}

                <button
                  onClick={() => {
                    stopCamera();
                    setStep('info');
                  }}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg mx-auto block"
                >
                  Back
                </button>
              </div>

              {/* Debug Info */}
              <div className="mt-6 bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold mb-2 text-sm">Debug Information:</h4>
                <div className="text-xs space-y-1">
                  <p>Camera Ready: {isCameraReady ? '✅ Yes' : '❌ No'}</p>
                  <p>Stream: {stream ? '✅ Active' : '❌ None'}</p>
                  <p>Video Element: {videoRef.current ? '✅ Ready' : '❌ Not Ready'}</p>
                  {videoRef.current && (
                    <>
                      <p>Video Dimensions: {videoRef.current.videoWidth || 0} x {videoRef.current.videoHeight || 0}</p>
                      <p>Video Ready State: {videoRef.current.readyState}</p>
                      <p>Video Paused: {videoRef.current.paused ? '❌ Yes' : '✅ No'}</p>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                <strong>Tips for best results:</strong> Look directly at the camera, ensure good lighting, 
                and make sure your face is clearly visible without sunglasses or masks.
              </p>
            </div>
          </div>
        )}

        {/* Step 3: Results */}
        {step === 'results' && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                🎉 Your Photos Found!
              </h2>
              <p className="text-gray-600">
                We found <span className="font-semibold text-green-600">{matchedImages.length}</span> photos with your face
              </p>
              {visitorData && (
                <p className="text-sm text-gray-500 mt-1">
                  Welcome, {visitorData.name}!
                </p>
              )}
            </div>

            {matchedImages.length === 0 ? (
              <div className="text-center py-8">
                <ImageIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No matches found</h3>
                <p className="text-gray-600 mb-6">
                  We couldn't find any photos with your face in this event.
                  This might be due to lighting, angle, or photo quality.
                </p>
                <div className="space-y-3">
                  <button
                    onClick={() => {
                      setStep('info');
                      setMatchedImages([]);
                      setVisitorId(null);
                    }}
                    className="btn-primary"
                  >
                    Try Again
                  </button>
                  <p className="text-xs text-gray-500">
                    Tip: Make sure your face was clearly visible in the event photos
                  </p>
                </div>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                  {matchedImages.map((image, index) => {
                    const backendUrl = process.env.NEXT_PUBLIC_API_URL?.replace('/api', '') || 'http://localhost:5000';
                    return (
                    <div key={image.image_id} className="relative group">
                      <img
                        src={`${backendUrl}/${image.image_path}`}
                        alt={`Photo ${index + 1}`}
                        className="w-full h-48 object-cover rounded-lg shadow-sm"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity rounded-lg flex items-center justify-center">
                        <button
                          onClick={() => downloadImage(image.image_path, image.filename)}
                          className="opacity-0 group-hover:opacity-100 transition-opacity bg-white text-gray-900 px-4 py-2 rounded-lg font-medium"
                        >
                          Download
                        </button>
                      </div>
                      <div className="absolute top-2 right-2 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                        {Math.round(image.confidence * 100)}% match
                      </div>
                    </div>
                    );
                  })}
                </div>
                
                <div className="flex gap-4">
                  <button
                    onClick={() => {
                      matchedImages.forEach((image, index) => {
                        setTimeout(() => {
                          downloadImage(image.image_path, `photo-${index + 1}-${image.filename}`);
                        }, index * 500);
                      });
                    }}
                    className="flex-1 btn-primary flex items-center justify-center gap-2"
                  >
                    <Upload className="h-5 w-5" />
                    Download All Photos
                  </button>
                  
                  <button
                    onClick={() => {
                      setStep('info');
                      setMatchedImages([]);
                      setVisitorId(null);
                    }}
                    className="btn-secondary"
                  >
                    Start Over
                  </button>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

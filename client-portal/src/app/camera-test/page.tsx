'use client';

import { useState, useRef, useEffect } from 'react';
import { Camera, Download, RotateCcw } from 'lucide-react';

export default function CameraTestPage() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const startCamera = async () => {
    try {
      setError(null);
      console.log('Requesting camera access...');
      
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640, min: 320 },
          height: { ideal: 480, min: 240 },
          facingMode: 'user'
        },
        audio: false
      });

      console.log('Camera stream obtained:', mediaStream);
      setStream(mediaStream);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        
        videoRef.current.onloadedmetadata = () => {
          console.log('Video metadata loaded');
          console.log('Video dimensions:', videoRef.current?.videoWidth, 'x', videoRef.current?.videoHeight);
          
          videoRef.current?.play().then(() => {
            console.log('Video is playing');
            setIsCameraActive(true);
          }).catch(err => {
            console.error('Failed to play video:', err);
            setIsCameraActive(true); // Still allow capture
          });
        };

        videoRef.current.onerror = (err) => {
          console.error('Video error:', err);
          setError('Video playback error');
        };
      }
    } catch (err: any) {
      console.error('Camera error:', err);
      setError(`Camera access failed: ${err.message}`);
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => {
        console.log('Stopping track:', track.kind);
        track.stop();
      });
      setStream(null);
      setIsCameraActive(false);
    }
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) {
      console.error('Video or canvas ref not available');
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) {
      console.error('Canvas context not available');
      return;
    }

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth || 640;
    canvas.height = video.videoHeight || 480;

    console.log('Capturing photo with dimensions:', canvas.width, 'x', canvas.height);

    // Draw the video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert to data URL
    const imageDataUrl = canvas.toDataURL('image/jpeg', 0.8);
    setCapturedImage(imageDataUrl);
    
    console.log('Photo captured, data URL length:', imageDataUrl.length);
  };

  const downloadPhoto = () => {
    if (!capturedImage) return;

    const link = document.createElement('a');
    link.href = capturedImage;
    link.download = `selfie-${Date.now()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetCapture = () => {
    setCapturedImage(null);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-center mb-6">Camera Test</h1>
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          <div className="space-y-6">
            {/* Camera Controls */}
            <div className="flex justify-center gap-4">
              {!isCameraActive ? (
                <button
                  onClick={startCamera}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center gap-2 font-medium"
                >
                  <Camera className="h-5 w-5" />
                  Start Camera
                </button>
              ) : (
                <button
                  onClick={stopCamera}
                  className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg flex items-center gap-2 font-medium"
                >
                  Stop Camera
                </button>
              )}
            </div>

            {/* Video Display */}
            <div className="flex justify-center">
              <div className="relative">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className="border-2 border-gray-300 rounded-lg max-w-full"
                  style={{
                    width: '640px',
                    height: '480px',
                    backgroundColor: '#f3f4f6',
                    objectFit: 'cover'
                  }}
                />
                
                {isCameraActive && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                    <button
                      onClick={capturePhoto}
                      className="bg-white hover:bg-gray-100 text-gray-800 px-4 py-2 rounded-full shadow-lg flex items-center gap-2 font-medium"
                    >
                      <Camera className="h-4 w-4" />
                      Capture
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Hidden Canvas */}
            <canvas ref={canvasRef} className="hidden" />

            {/* Captured Image */}
            {capturedImage && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-center">Captured Photo</h3>
                <div className="flex justify-center">
                  <img
                    src={capturedImage}
                    alt="Captured selfie"
                    className="border-2 border-gray-300 rounded-lg max-w-md"
                  />
                </div>
                <div className="flex justify-center gap-4">
                  <button
                    onClick={downloadPhoto}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Download
                  </button>
                  <button
                    onClick={resetCapture}
                    className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
                  >
                    <RotateCcw className="h-4 w-4" />
                    Take Another
                  </button>
                </div>
              </div>
            )}

            {/* Debug Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold mb-2">Debug Information:</h4>
              <div className="text-sm space-y-1">
                <p>Camera Active: {isCameraActive ? '✅ Yes' : '❌ No'}</p>
                <p>Stream: {stream ? '✅ Active' : '❌ None'}</p>
                <p>Video Element: {videoRef.current ? '✅ Ready' : '❌ Not Ready'}</p>
                <p>Canvas Element: {canvasRef.current ? '✅ Ready' : '❌ Not Ready'}</p>
                {videoRef.current && (
                  <>
                    <p>Video Dimensions: {videoRef.current.videoWidth || 0} x {videoRef.current.videoHeight || 0}</p>
                    <p>Video Ready State: {videoRef.current.readyState}</p>
                  </>
                )}
              </div>
            </div>

            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-900 mb-2">Instructions:</h4>
              <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                <li>Click "Start Camera" to begin</li>
                <li>Allow camera permissions when prompted</li>
                <li>You should see your video feed in the box above</li>
                <li>Click "Capture" to take a photo</li>
                <li>Download or retake as needed</li>
              </ol>
            </div>

            {/* Browser Compatibility */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-900 mb-2">Browser Requirements:</h4>
              <ul className="text-sm text-yellow-800 space-y-1 list-disc list-inside">
                <li>Modern browser with WebRTC support</li>
                <li>HTTPS connection (or localhost for testing)</li>
                <li>Camera permissions granted</li>
                <li>No other applications using the camera</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

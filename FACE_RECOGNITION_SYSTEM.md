# Face Recognition System Implementation

## 🎯 Overview

This document describes the complete face recognition system implementation for the Photo-Cap application. The system allows studios to create events, upload photos, and enable visitors to find their photos using face recognition technology.

## 🏗️ Architecture

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Studio Panel │    │  Client Portal  │    │  Admin Panel    │
│  (port 3001)   │    │  (port 3002)    │    │  (port 3000)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼───────────────┐
                    │     Node.js Backend        │
                    │      (port 5000)           │
                    │  - Event Management        │
                    │  - Authentication          │
                    │  - Image Upload            │
                    └─────────────┬───────────────┘
                                  │
                    ┌─────────────▼───────────────┐
                    │  Python Face Service       │
                    │      (port 8000)           │
                    │  - Face Detection          │
                    │  - Face Matching           │
                    │  - Descriptor Storage      │
                    └─────────────┬───────────────┘
                                  │
                    ┌─────────────▼───────────────┐
                    │    PostgreSQL Database     │
                    │  - User Data               │
                    │  - Event Data              │
                    │  - Face Descriptors        │
                    └─────────────────────────────┘
```

## 🔄 Workflow

### 1. Event Creation (Studio)
1. Studio logs into panel
2. Creates new event with name/description
3. System generates unique ID, QR code, and access link
4. Studio uploads event photos
5. Python service processes photos and extracts face descriptors

### 2. Visitor Access
1. Visitor scans QR code or opens event link
2. Enters name and contact information
3. Takes webcam selfie
4. Python service matches selfie against event faces
5. System returns filtered gallery with matching photos

## 📊 Database Schema

### New Tables Added

```sql
-- Events (replaces individual clients)
events (
  id, studio_id, name, description, is_public,
  unique_id, access_link, qr_code, created_at, updated_at
)

-- Event images with face processing status
event_images (
  id, event_id, filename, original_name, path, size,
  mime_type, face_count, processing_status, uploaded_at
)

-- Face descriptors (128-dimensional vectors)
face_descriptors (
  id, image_id, descriptor_vector[], face_location,
  confidence, face_landmarks, created_at
)

-- Visitor selfies and data
event_visitors (
  id, event_id, name, contact, email, selfie_path,
  selfie_descriptor[], processing_status, visited_at
)

-- Matched photos for visitors
visitor_matches (
  id, visitor_id, image_id, face_descriptor_id,
  confidence_score, match_threshold, matched_at
)
```

## 🔧 API Endpoints

### Node.js Backend (port 5000)

#### Event Management
- `POST /api/events` - Create event
- `GET /api/events` - Get studio events
- `GET /api/events/:id` - Get event details
- `PUT /api/events/:id` - Update event
- `DELETE /api/events/:id` - Delete event

#### Image Upload
- `POST /api/events/:id/images/bulk-upload` - Upload event images
- `GET /api/events/:id/images` - Get event images

#### Public Access
- `GET /api/client-portal/event/:uniqueId` - Get event info (public)

### Python Face Service (port 8000)

#### Face Processing
- `POST /api/face/process-images` - Extract faces from images
- `POST /api/face/visitor-selfie` - Process visitor selfie
- `POST /api/face/match-visitor` - Match visitor faces

#### Data Retrieval
- `GET /api/face/visitor/:id/matches` - Get matched photos
- `GET /api/face/event/:id/stats` - Get face statistics

## 🚀 Setup Instructions

### Prerequisites
- Node.js 18+
- Python 3.8+
- PostgreSQL
- Git

### Quick Setup
```bash
# Run the automated setup script
./setup-face-recognition.sh

# Or manual setup:
# 1. Update database schema
cd backend
npm run db:generate
npm run db:push
npm run db:seed

# 2. Setup Python service
cd ../python-face-service
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 3. Configure environment files
cp .env.example .env
# Edit .env files with your database credentials

# 4. Start all services
./start-all.sh
```

### Environment Configuration

#### Backend (.env)
```env
DATABASE_URL=postgresql://user:pass@localhost:5432/photocap
JWT_SECRET=your-secret-key
PYTHON_FACE_SERVICE_URL=http://localhost:8000
```

#### Python Service (.env)
```env
DATABASE_URL=postgresql://user:pass@localhost:5432/photocap
SECRET_KEY=your-secret-key
FACE_RECOGNITION_TOLERANCE=0.6
DEBUG=True
```

## 🧪 Testing the System

### 1. Studio Workflow
1. Access studio panel: http://localhost:3001
2. Login with: `<EMAIL>` / `studio123`
3. Create new event: "Wedding Reception"
4. Upload sample photos with faces
5. Wait for face processing to complete
6. Copy event QR code or link

### 2. Visitor Workflow
1. Open event link: http://localhost:3002/event/[unique-id]
2. Enter visitor details
3. Take webcam selfie
4. View matched photos

### 3. API Testing
```bash
# Test Python service health
curl http://localhost:8000/api/health

# Test Node.js backend health
curl http://localhost:5000/health

# View API documentation
open http://localhost:8000/docs
```

## 🔒 Security Features

- Face descriptors are mathematical vectors (not images)
- Visitor selfies can be auto-deleted after processing
- JWT authentication for studio access
- Input validation and sanitization
- CORS protection
- Rate limiting on face processing

## 📈 Performance Considerations

### Face Processing
- ~1-2 seconds per image (depending on size/faces)
- Batch processing for multiple images
- Asynchronous processing to avoid timeouts
- Memory usage: ~500MB base + ~50MB per concurrent image

### Optimization Tips
- Use smaller image sizes for faster processing
- Adjust `FACE_RECOGNITION_TOLERANCE` for accuracy vs speed
- Enable image compression in upload middleware
- Consider GPU acceleration for large volumes

## 🚨 Troubleshooting

### Common Issues

#### Python Dependencies
```bash
# If dlib installation fails
pip install cmake
pip install dlib

# If face_recognition fails
pip install --upgrade setuptools
pip install face_recognition
```

#### Database Issues
```bash
# Reset database
cd backend
npm run db:push --force-reset
npm run db:seed
```

#### Face Recognition Not Working
- Check image quality and lighting
- Ensure faces are at least 50x50 pixels
- Adjust tolerance in Python service .env
- Check Python service logs

#### Memory Issues
- Reduce batch size for image processing
- Increase system memory
- Process images sequentially instead of parallel

## 📝 Development Notes

### Adding New Features
1. Database changes: Update `backend/prisma/schema.prisma`
2. Python service: Add endpoints in `python-face-service/app/api/routes.py`
3. Node.js backend: Add controllers in `backend/src/controllers/`
4. Frontend: Update components in respective client folders

### Code Structure
```
backend/
├── src/
│   ├── controllers/event.controller.ts
│   ├── services/event.service.ts
│   ├── routes/event.routes.ts
│   └── validations/event.validation.ts

python-face-service/
├── app/
│   ├── api/routes.py
│   ├── services/face_service.py
│   ├── models/face_models.py
│   └── schemas/face_schemas.py
```

## 🔄 Future Enhancements

- Real-time face processing progress
- Advanced face recognition models
- Cloud storage integration (AWS S3)
- Mobile app for easier visitor access
- Analytics dashboard for studios
- Bulk visitor import/export
- Advanced privacy controls

## 📞 Support

For issues or questions:
1. Check logs in `python-face-service/logs/`
2. Review API documentation at http://localhost:8000/docs
3. Test individual components using provided scripts
4. Verify database connections and environment variables

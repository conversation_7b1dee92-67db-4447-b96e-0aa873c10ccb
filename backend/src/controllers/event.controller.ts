import { Request, Response, NextFunction } from 'express';
import * as eventService from '../services/event.service';
import { ApiError } from '../utils/apiError';
import {
  createEventSchema,
  updateEventSchema,
  bulkUploadEventImagesSchema
} from '../validations/event.validation';

// Get all events for studio
export const getStudioEvents = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const events = await eventService.getStudioEvents(req.user.id);

    res.status(200).json({
      success: true,
      message: 'Events retrieved successfully',
      data: events,
    });
  } catch (error) {
    next(error);
  }
};

// Get event by ID
export const getEventById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { eventId } = req.params;
    const event = await eventService.getEventById(eventId, req.user.id);

    res.status(200).json({
      success: true,
      message: 'Event retrieved successfully',
      data: event,
    });
  } catch (error) {
    next(error);
  }
};

// Get event by unique ID (public access)
export const getEventByUniqueId = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { uniqueId } = req.params;
    const event = await eventService.getEventByUniqueId(uniqueId);

    res.status(200).json({
      success: true,
      message: 'Event retrieved successfully',
      data: event,
    });
  } catch (error) {
    next(error);
  }
};

// Create new event
export const createEvent = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { error } = createEventSchema.validate(req.body);
    if (error) {
      throw new ApiError(400, error.details[0].message);
    }

    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const event = await eventService.createEvent(req.user.id, req.body);

    res.status(201).json({
      success: true,
      message: 'Event created successfully',
      data: event,
    });
  } catch (error) {
    next(error);
  }
};

// Update event
export const updateEvent = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { error } = updateEventSchema.validate(req.body);
    if (error) {
      throw new ApiError(400, error.details[0].message);
    }

    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { eventId } = req.params;
    const event = await eventService.updateEvent(eventId, req.user.id, req.body);

    res.status(200).json({
      success: true,
      message: 'Event updated successfully',
      data: event,
    });
  } catch (error) {
    next(error);
  }
};

// Delete event
export const deleteEvent = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { eventId } = req.params;
    const result = await eventService.deleteEvent(eventId, req.user.id);

    res.status(200).json({
      success: true,
      message: result.message,
    });
  } catch (error) {
    next(error);
  }
};

// Regenerate QR code
export const regenerateQRCode = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { eventId } = req.params;
    const event = await eventService.regenerateEventQR(eventId, req.user.id);

    res.status(200).json({
      success: true,
      message: 'QR code regenerated successfully',
      data: { qrCode: event.qrCode },
    });
  } catch (error) {
    next(error);
  }
};

// Bulk upload images to event
export const bulkUploadEventImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { error } = bulkUploadEventImagesSchema.validate(req.body);
    if (error) {
      throw new ApiError(400, error.details[0].message);
    }

    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { eventId, descriptions } = req.body;
    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      throw new ApiError(400, 'No files uploaded');
    }

    // Set a longer timeout for bulk uploads
    req.setTimeout(300000); // 5 minutes
    res.setTimeout(300000); // 5 minutes

    console.log(`Starting bulk upload of ${files.length} images for event ${eventId}`);

    const images = await eventService.uploadEventImages(
      req.user.id,
      eventId,
      files,
      descriptions
    );

    console.log(`Successfully uploaded ${images.length} images`);

    res.status(201).json({
      success: true,
      message: `${images.length} images uploaded successfully. Face processing started.`,
      data: images,
    });
  } catch (error) {
    console.error('Bulk upload error:', error);
    next(error);
  }
};

// Get event statistics
export const getEventStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { eventId } = req.params;
    const stats = await eventService.getEventStats(eventId, req.user.id);

    res.status(200).json({
      success: true,
      message: 'Event statistics retrieved successfully',
      data: stats,
    });
  } catch (error) {
    next(error);
  }
};

// Reprocess event images
export const reprocessEventImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { eventId } = req.params;
    const result = await eventService.reprocessEventImages(eventId, req.user.id);

    res.status(200).json({
      success: true,
      message: 'Images reprocessed successfully',
      data: result
    });
  } catch (error) {
    next(error);
  }
};

// Get event images
export const getEventImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { eventId } = req.params;
    
    // Verify event belongs to studio
    const event = await eventService.getEventById(eventId, req.user.id);
    
    res.status(200).json({
      success: true,
      message: 'Event images retrieved successfully',
      data: event.images,
    });
  } catch (error) {
    next(error);
  }
};

// Delete event image
export const deleteEventImage = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { imageId } = req.params;
    
    // TODO: Implement image deletion with face descriptor cleanup
    // This would involve calling Python service to clean up face data
    
    res.status(200).json({
      success: true,
      message: 'Image deletion not yet implemented',
    });
  } catch (error) {
    next(error);
  }
};

import { Request, Response, NextFunction } from 'express';
import * as subscriptionService from '../services/subscription.service';
import { ApiError } from '../utils/apiError';

// Get all subscription plans
export const getAllPlans = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const plans = await subscriptionService.getAllPlans();

    res.status(200).json({
      success: true,
      message: 'Subscription plans retrieved successfully',
      data: plans,
    });
  } catch (error) {
    next(error);
  }
};

// Get studio's current subscription
export const getStudioSubscription = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const subscription = await subscriptionService.getStudioSubscription(req.user.id);

    res.status(200).json({
      success: true,
      message: 'Subscription retrieved successfully',
      data: subscription,
    });
  } catch (error) {
    next(error);
  }
};

// Create subscription for studio
export const createSubscription = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { planId } = req.body;

    if (!planId) {
      throw new ApiError(400, 'Plan ID is required');
    }

    const subscription = await subscriptionService.createStudioSubscription(req.user.id, planId);

    res.status(201).json({
      success: true,
      message: 'Subscription created successfully',
      data: subscription,
    });
  } catch (error) {
    next(error);
  }
};

// Update studio subscription
export const updateSubscription = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { planId } = req.body;

    if (!planId) {
      throw new ApiError(400, 'Plan ID is required');
    }

    const subscription = await subscriptionService.updateStudioSubscription(req.user.id, planId);

    res.status(200).json({
      success: true,
      message: 'Subscription updated successfully',
      data: subscription,
    });
  } catch (error) {
    next(error);
  }
};

// Cancel studio subscription
export const cancelSubscription = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const subscription = await subscriptionService.cancelStudioSubscription(req.user.id);

    res.status(200).json({
      success: true,
      message: 'Subscription cancelled successfully',
      data: subscription,
    });
  } catch (error) {
    next(error);
  }
};

// Admin: Get subscription analytics
export const getSubscriptionAnalytics = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const analytics = await subscriptionService.getSubscriptionAnalytics();

    res.status(200).json({
      success: true,
      message: 'Subscription analytics retrieved successfully',
      data: analytics,
    });
  } catch (error) {
    next(error);
  }
};

// Admin: Get all studio subscriptions
export const getAllStudioSubscriptions = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const subscriptions = await subscriptionService.getAllStudioSubscriptions();

    res.status(200).json({
      success: true,
      message: 'Studio subscriptions retrieved successfully',
      data: subscriptions,
    });
  } catch (error) {
    next(error);
  }
};

// Admin: Update studio subscription
export const updateStudioSubscriptionAdmin = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { studioId } = req.params;
    const { planId, status } = req.body;

    if (!planId && !status) {
      throw new ApiError(400, 'Plan ID or status is required');
    }

    let subscription;
    
    if (planId) {
      subscription = await subscriptionService.updateStudioSubscription(studioId, planId);
    } else if (status === 'CANCELLED') {
      subscription = await subscriptionService.cancelStudioSubscription(studioId);
    }

    res.status(200).json({
      success: true,
      message: 'Studio subscription updated successfully',
      data: subscription,
    });
  } catch (error) {
    next(error);
  }
};

// Admin: Create subscription plan
export const createPlan = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const planData = req.body;
    const plan = await subscriptionService.createPlan(planData);

    res.status(201).json({
      success: true,
      message: 'Subscription plan created successfully',
      data: plan,
    });
  } catch (error) {
    next(error);
  }
};

// Admin: Update subscription plan
export const updatePlan = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { planId } = req.params;
    const planData = req.body;
    const plan = await subscriptionService.updatePlan(planId, planData);

    res.status(200).json({
      success: true,
      message: 'Subscription plan updated successfully',
      data: plan,
    });
  } catch (error) {
    next(error);
  }
};

// Admin: Delete subscription plan
export const deletePlan = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { planId } = req.params;
    await subscriptionService.deletePlan(planId);

    res.status(200).json({
      success: true,
      message: 'Subscription plan deleted successfully',
    });
  } catch (error) {
    next(error);
  }
};

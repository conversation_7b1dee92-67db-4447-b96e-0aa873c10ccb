import { Request, Response, NextFunction } from 'express';
import * as visitorService from '../services/visitor.service';
import { ApiError } from '../utils/apiError';
import logger from '../utils/logger';

// PhotoCap-style visitor registration with face matching
export const registerVisitorPhotoCap = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { eventUniqueId } = req.params;
    const { name, phone, email } = req.body;

    // Validate required fields
    if (!name?.trim()) {
      throw new ApiError(400, 'Visitor name is required');
    }

    if (!phone?.trim()) {
      throw new ApiError(400, 'Phone number is required');
    }

    // Validate selfie file
    if (!req.file) {
      throw new ApiError(400, 'Selfie image is required');
    }

    // Validate file type
    if (!req.file.mimetype.startsWith('image/')) {
      throw new ApiError(400, 'File must be an image');
    }

    // Get client info
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';

    logger.info(`Processing FotoOwl registration for ${name} (${phone}) in event ${eventUniqueId}`);

    const result = await visitorService.registerVisitorAndMatch({
      eventUniqueId,
      name: name.trim(),
      phone: phone.trim(),
      email: email?.trim(),
      selfieFile: req.file,
      ipAddress,
      userAgent
    });

    res.status(200).json({
      success: true,
      message: result.message,
      data: {
        visitor: result.visitor,
        event: result.eventInfo,
        matches: {
          total: result.matches.length,
          images: result.matches,
          summary: result.matchSummary
        },
        isExistingVisitor: result.isExisting
      }
    });

  } catch (error) {
    logger.error(`Error in FotoOwl visitor registration: ${error}`);
    next(error);
  }
};

// Get visitor's matched photos
export const getVisitorMatches = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { visitorId } = req.params;

    if (!visitorId) {
      throw new ApiError(400, 'Visitor ID is required');
    }

    const result = await visitorService.getVisitorMatches(visitorId);

    res.status(200).json({
      success: true,
      message: `Found ${result.totalMatches} matched photos`,
      data: result
    });

  } catch (error) {
    logger.error(`Error getting visitor matches: ${error}`);
    next(error);
  }
};

// Get event information (public access)
export const getEventInfo = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { eventUniqueId } = req.params;

    if (!eventUniqueId) {
      throw new ApiError(400, 'Event unique ID is required');
    }

    const event = await visitorService.getEventByUniqueId(eventUniqueId);

    res.status(200).json({
      success: true,
      message: 'Event information retrieved successfully',
      data: event
    });

  } catch (error) {
    logger.error(`Error getting event info: ${error}`);
    next(error);
  }
};

// Update gallery access statistics
export const updateGalleryStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { visitorId } = req.params;
    const { imagesViewed, imagesDownloaded, sessionDuration } = req.body;

    if (!visitorId) {
      throw new ApiError(400, 'Visitor ID is required');
    }

    await visitorService.updateGalleryAccess(visitorId, {
      imagesViewed,
      imagesDownloaded,
      sessionDuration
    });

    res.status(200).json({
      success: true,
      message: 'Gallery access statistics updated successfully'
    });

  } catch (error) {
    logger.error(`Error updating gallery stats: ${error}`);
    next(error);
  }
};

// Download visitor's matched images (future implementation)
export const downloadVisitorImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { visitorId } = req.params;
    const { quality = 'original' } = req.query;

    if (!visitorId) {
      throw new ApiError(400, 'Visitor ID is required');
    }

    // Get visitor matches
    const result = await visitorService.getVisitorMatches(visitorId);
    
    if (result.images.length === 0) {
      throw new ApiError(404, 'No matched images found for this visitor');
    }

    // For now, return the list of images
    // TODO: Implement actual zip download functionality
    res.status(200).json({
      success: true,
      message: `${result.images.length} images ready for download`,
      data: {
        visitor: result.visitor,
        event: result.event,
        images: result.images,
        downloadInfo: {
          quality,
          totalImages: result.images.length,
          estimatedSize: `${result.images.length * 2}MB` // Rough estimate
        }
      }
    });

  } catch (error) {
    logger.error(`Error preparing visitor image download: ${error}`);
    next(error);
  }
};

// Get visitor registration status
export const getVisitorStatus = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { eventUniqueId, phone } = req.params;

    if (!eventUniqueId || !phone) {
      throw new ApiError(400, 'Event unique ID and phone number are required');
    }

    // Check if visitor exists for this event
    const event = await visitorService.getEventByUniqueId(eventUniqueId);
    
    // This is a simplified check - in production you might want more sophisticated lookup
    res.status(200).json({
      success: true,
      data: {
        eventExists: !!event,
        eventName: event?.name,
        canRegister: !!event
      }
    });

  } catch (error) {
    logger.error(`Error checking visitor status: ${error}`);
    next(error);
  }
};

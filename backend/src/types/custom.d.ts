export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterStudioRequest {
  email: string;
  password: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
}

export interface UpdateStudioStatusRequest {
  status: 'APPROVED' | 'REJECTED';
}

export interface CreateStudioRequest {
  email: string;
  password: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
  status?: 'PENDING' | 'APPROVED' | 'REJECTED';
}

export interface UpdateStudioRequest {
  name?: string;
  email?: string;
  description?: string;
  phone?: string;
  address?: string;
}

import { Router } from 'express';
import * as subscriptionController from '../controllers/subscription.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';

const router = Router();

// Public routes
router.get('/plans', subscriptionController.getAllPlans);

// Studio routes (protected)
router.use(authenticate);

// Studio subscription management
router.get('/my-subscription', authorize('STUDIO'), subscriptionController.getStudioSubscription);
router.post('/subscribe', authorize('STUDIO'), subscriptionController.createSubscription);
router.put('/update-plan', authorize('STUDIO'), subscriptionController.updateSubscription);
router.post('/cancel', authorize('STUDIO'), subscriptionController.cancelSubscription);

// Admin routes
router.get('/admin/analytics', authorize('ADMIN'), subscriptionController.getSubscriptionAnalytics);
router.get('/admin/subscriptions', authorize('ADMIN'), subscriptionController.getAllStudioSubscriptions);
router.put('/admin/subscriptions/:studioId', authorize('ADMIN'), subscriptionController.updateStudioSubscriptionAdmin);

// Admin plan management routes
router.post('/admin/plans', authorize('ADMIN'), subscriptionController.createPlan);
router.put('/admin/plans/:planId', authorize('ADMIN'), subscriptionController.updatePlan);
router.delete('/admin/plans/:planId', authorize('ADMIN'), subscriptionController.deletePlan);

export default router;

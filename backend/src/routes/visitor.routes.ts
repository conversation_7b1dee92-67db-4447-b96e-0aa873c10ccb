import { Router } from 'express';
import * as visitorController from '../controllers/visitor.controller';
import { upload } from '../middleware/upload.middleware';
import multer from 'multer';

const router = Router();

// Configure multer for selfie uploads
const selfieUpload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 1
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Public routes (no authentication required)

// Get event information by unique ID
router.get('/event/:eventUniqueId/info', visitorController.getEventInfo);

// Check visitor registration status
router.get('/event/:eventUniqueId/visitor/:phone/status', visitorController.getVisitorStatus);

// FotoOwl-style visitor registration with face matching
router.post(
  '/event/:eventUniqueId/register',
  selfieUpload.single('selfie'),
  visitorController.registerVisitorFotoOwlStyle
);

// Get visitor's matched photos
router.get('/visitor/:visitorId/matches', visitorController.getVisitorMatches);

// Update gallery access statistics
router.post('/visitor/:visitorId/gallery-stats', visitorController.updateGalleryStats);

// Download visitor's matched images
router.get('/visitor/:visitorId/download', visitorController.downloadVisitorImages);

export default router;

import { Router } from 'express';
import * as eventController from '../controllers/event.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';
import {
  requireActiveSubscription,
  checkStorageLimit,
  updateUsageStats
} from '../middleware/planLimits.middleware';
import { upload } from '../middleware/upload.middleware';

const router = Router();

// Studio routes (protected - studio access only)
router.use(authenticate);
router.use(authorize('STUDIO'));
router.use(requireActiveSubscription);

// Event CRUD operations
router.post('/', eventController.createEvent);
router.get('/', eventController.getStudioEvents);
router.get('/:eventId', eventController.getEventById);
router.put('/:eventId', eventController.updateEvent);
router.delete('/:eventId', eventController.deleteEvent);

// Event management
router.post('/:eventId/regenerate-qr', eventController.regenerateQRCode);
router.get('/:eventId/stats', eventController.getEventStats);
router.post('/:eventId/reprocess', eventController.reprocessEventImages);

// Image management for events
router.post(
  '/:eventId/images/bulk-upload',
  checkStorageLimit,
  upload.array('images', 50),
  eventController.bulkUploadEventImages,
  updateUsageStats
);
router.get('/:eventId/images', eventController.getEventImages);
router.delete('/images/:imageId', eventController.deleteEventImage, updateUsageStats);

export default router;

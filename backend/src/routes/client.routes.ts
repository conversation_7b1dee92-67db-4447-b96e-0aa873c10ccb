import { Router } from 'express';
import * as clientController from '../controllers/client.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';
import { upload } from '../middleware/upload.middleware';
import {
  checkClientLimit,
  checkStorageLimit,
  requireActiveSubscription,
  updateUsageStats
} from '../middleware/planLimits.middleware';

const router = Router();

// Studio routes (protected - studio access only)
router.use(authenticate);
router.use(authorize('STUDIO'));
router.use(requireActiveSubscription);

// Basic client management routes (working)
router.post('/', checkClientLimit, clientController.createClient, updateUsageStats);
router.get('/', clientController.getStudioClients);
router.get('/:clientId', clientController.getClientById);
router.put('/:clientId', clientController.updateClient);
router.delete('/:clientId', clientController.deleteClient, updateUsageStats);

// Client password and QR code management
router.post('/:clientId/reset-password', clientController.resetClientPassword);
router.post('/:clientId/regenerate-qr', clientController.regenerateQRCode);

// Image management routes
router.post('/images/bulk-upload', checkStorageLimit, upload.array('images', 50), clientController.bulkUploadImages, updateUsageStats);
router.get('/:clientId/images', clientController.getClientImages);
router.put('/images/:imageId', clientController.updateImage);
router.delete('/images/:imageId', clientController.deleteImage, updateUsageStats);

export default router;

import { Router, Request, Response, NextFunction } from 'express';
import { authenticate, authorize } from '../middleware/auth.middleware';
import * as downloadService from '../services/download.service';
import { ApiError } from '../utils/apiError';

const router = Router();

// All routes require authentication
router.use(authenticate);

/**
 * Download single image with quality option
 * GET /api/download/image/:imageId?quality=original|optimized
 */
router.get('/image/:imageId', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { imageId } = req.params;
    const quality = (req.query.quality as 'original' | 'optimized') || 'original';

    if (!['original', 'optimized'].includes(quality)) {
      throw new ApiError(400, 'Invalid quality parameter. Use "original" or "optimized"');
    }

    const imagePath = await downloadService.downloadSingleImage(imageId, quality);
    
    // Get image info for proper headers
    const image = await prisma.image.findUnique({
      where: { id: imageId }
    });

    if (!image) {
      throw new ApiError(404, 'Image not found');
    }

    // Set appropriate headers
    res.setHeader('Content-Type', image.mimeType);
    res.setHeader('Content-Disposition', `attachment; filename="${image.originalName}"`);
    res.setHeader('X-Download-Quality', quality);

    // Send file
    res.sendFile(imagePath);
  } catch (error) {
    next(error);
  }
});

/**
 * Download multiple images as ZIP
 * POST /api/download/images/zip
 * Body: { imageIds: string[], quality: 'original'|'optimized', includeMetadata: boolean }
 */
router.post('/images/zip', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { imageIds, quality = 'original', includeMetadata = false } = req.body;

    if (!Array.isArray(imageIds) || imageIds.length === 0) {
      throw new ApiError(400, 'imageIds array is required');
    }

    if (!['original', 'optimized'].includes(quality)) {
      throw new ApiError(400, 'Invalid quality parameter');
    }

    await downloadService.downloadImagesAsZip(imageIds, res, {
      quality,
      format: 'zip',
      includeMetadata
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Download all client images
 * GET /api/download/client/:clientId/all?quality=original|optimized&metadata=true|false
 */
router.get('/client/:clientId/all', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { clientId } = req.params;
    const quality = (req.query.quality as 'original' | 'optimized') || 'original';
    const includeMetadata = req.query.metadata === 'true';

    if (!['original', 'optimized'].includes(quality)) {
      throw new ApiError(400, 'Invalid quality parameter');
    }

    await downloadService.downloadAllClientImages(clientId, res, {
      quality,
      format: 'zip',
      includeMetadata
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Get download statistics for studio
 * GET /api/download/stats
 */
router.get('/stats', authorize('STUDIO', 'ADMIN'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const stats = await downloadService.getDownloadStats(req.user.id);

    res.status(200).json({
      success: true,
      message: 'Download statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    next(error);
  }
});

export default router;

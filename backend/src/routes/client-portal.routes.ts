import { Router } from 'express';
import * as clientController from '../controllers/client.controller';
import { authenticate } from '../middleware/auth.middleware';

const router = Router();

// Public routes (no authentication required)
router.post('/login', clientController.clientLogin);

// Event access routes (public)
router.get('/event/:uniqueId', async (req, res, next) => {
  try {
    const { uniqueId } = req.params;
    const eventService = await import('../services/event.service');
    const event = await eventService.getEventByUniqueId(uniqueId);

    res.status(200).json({
      success: true,
      message: 'Event retrieved successfully',
      data: event,
    });
  } catch (error) {
    next(error);
  }
});

// Protected routes (client access only)
router.use(authenticate);

// Client's own images
router.get('/my-images', clientController.getMyImages);
router.get('/download-all', clientController.downloadAllImages);

export default router;

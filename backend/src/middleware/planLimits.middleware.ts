import { Request, Response, NextFunction } from 'express';
import * as subscriptionService from '../services/subscription.service';
import { ApiError } from '../utils/apiError';

// Middleware to check if studio can create clients
export const checkClientLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user || req.user.role !== 'STUDIO') {
      return next();
    }

    // Get current client count
    const { prisma } = require('../utils/prisma');
    const clientCount = await prisma.client.count({
      where: { studioId: req.user.id },
    });

    await subscriptionService.checkPlanLimits(req.user.id, 'create_client', clientCount);
    next();
  } catch (error) {
    next(error);
  }
};

// Middleware to check if studio can create users
export const checkUserLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user || req.user.role !== 'STUDIO') {
      return next();
    }

    // Get current user count
    const { prisma } = require('../utils/prisma');
    const userCount = await prisma.user.count({
      where: { studioId: req.user.id },
    });

    await subscriptionService.checkPlanLimits(req.user.id, 'create_user', userCount);
    next();
  } catch (error) {
    next(error);
  }
};

// Middleware to check storage limits before image upload
export const checkStorageLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user || req.user.role !== 'STUDIO') {
      return next();
    }

    await subscriptionService.checkPlanLimits(req.user.id, 'upload_image');
    next();
  } catch (error) {
    next(error);
  }
};

// Middleware to check if feature is available in current plan
export const checkFeatureAccess = (feature: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user || req.user.role !== 'STUDIO') {
        return next();
      }

      const subscription = await subscriptionService.getStudioSubscription(req.user.id);
      
      if (!subscription || subscription.status === 'EXPIRED' || subscription.status === 'CANCELLED') {
        throw new ApiError(403, 'No active subscription found');
      }

      const plan = subscription.plan;

      switch (feature) {
        case 'custom_branding':
          if (!plan.customBranding) {
            throw new ApiError(403, 'Custom branding is not available in your current plan');
          }
          break;
        
        case 'advanced_analytics':
          if (!plan.advancedAnalytics) {
            throw new ApiError(403, 'Advanced analytics is not available in your current plan');
          }
          break;
        
        case 'api_access':
          if (!plan.apiAccess) {
            throw new ApiError(403, 'API access is not available in your current plan');
          }
          break;
        
        case 'white_label':
          if (!plan.whiteLabel) {
            throw new ApiError(403, 'White label feature is not available in your current plan');
          }
          break;
        
        case 'custom_domain':
          if (!plan.customDomain) {
            throw new ApiError(403, 'Custom domain is not available in your current plan');
          }
          break;
        
        default:
          break;
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

// Middleware to update usage stats after successful operations
export const updateUsageStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user || req.user.role !== 'STUDIO') {
      return next();
    }

    // Update usage stats in background
    subscriptionService.updateUsageStats(req.user.id).catch(console.error);
    next();
  } catch (error) {
    next(error);
  }
};

// Middleware to check subscription status
export const requireActiveSubscription = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user || req.user.role !== 'STUDIO') {
      return next();
    }

    const subscription = await subscriptionService.getStudioSubscription(req.user.id);
    
    if (!subscription) {
      throw new ApiError(403, 'No subscription found. Please select a plan to continue.');
    }

    if (subscription.status === 'EXPIRED') {
      throw new ApiError(403, 'Your subscription has expired. Please renew to continue.');
    }

    if (subscription.status === 'CANCELLED') {
      throw new ApiError(403, 'Your subscription has been cancelled. Please reactivate to continue.');
    }

    // Check if trial has expired
    if (subscription.status === 'TRIAL' && subscription.trialEnd) {
      const now = new Date();
      const trialEnd = new Date(subscription.trialEnd);
      
      if (now > trialEnd) {
        // Update subscription status to expired
        const { prisma } = require('../utils/prisma');
        await prisma.studioSubscription.update({
          where: { id: subscription.id },
          data: { status: 'EXPIRED' },
        });
        
        throw new ApiError(403, 'Your trial period has expired. Please upgrade to continue.');
      }
    }

    next();
  } catch (error) {
    next(error);
  }
};

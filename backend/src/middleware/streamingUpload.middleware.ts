import { Request, Response, NextFunction } from 'express';
import { Readable } from 'stream';
import { pipeline } from 'stream/promises';
import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';

/**
 * Streaming upload middleware for large files
 * Processes files in chunks to reduce memory usage
 */

interface StreamingUploadOptions {
  maxFileSize: number;
  allowedMimeTypes: string[];
  outputDir: string;
  enableCompression: boolean;
  compressionQuality: number;
}

const defaultOptions: StreamingUploadOptions = {
  maxFileSize: 50 * 1024 * 1024, // 50MB
  allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  outputDir: 'uploads/images',
  enableCompression: true,
  compressionQuality: 85,
};

/**
 * Create streaming upload middleware
 */
export const createStreamingUpload = (options: Partial<StreamingUploadOptions> = {}) => {
  const config = { ...defaultOptions, ...options };

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const contentType = req.headers['content-type'];
      const contentLength = parseInt(req.headers['content-length'] || '0');

      // Validate content type
      if (!contentType || !config.allowedMimeTypes.some(type => contentType.includes(type))) {
        return res.status(400).json({
          success: false,
          message: 'Invalid file type. Only images are allowed.',
        });
      }

      // Validate file size
      if (contentLength > config.maxFileSize) {
        return res.status(400).json({
          success: false,
          message: `File too large. Maximum size is ${config.maxFileSize / 1024 / 1024}MB.`,
        });
      }

      // Generate unique filename
      const timestamp = Date.now();
      const randomId = Math.round(Math.random() * 1E9);
      const extension = getExtensionFromMimeType(contentType);
      const filename = `stream-${timestamp}-${randomId}${extension}`;
      const outputPath = path.join(process.cwd(), config.outputDir, filename);

      // Ensure output directory exists
      await fs.mkdir(path.dirname(outputPath), { recursive: true });

      let processedSize = 0;
      const startTime = Date.now();

      // Create processing pipeline
      const processingPipeline = config.enableCompression
        ? createCompressionPipeline(config.compressionQuality)
        : createPassthroughPipeline();

      // Stream processing with progress tracking
      const trackingStream = new Readable({
        read() {
          // This will be handled by the pipeline
        }
      });

      // Track upload progress
      req.on('data', (chunk: Buffer) => {
        processedSize += chunk.length;
        const progress = Math.round((processedSize / contentLength) * 100);
        
        // Emit progress event (can be used for real-time updates)
        req.app.emit('upload-progress', {
          filename,
          progress,
          processedSize,
          totalSize: contentLength,
        });

        trackingStream.push(chunk);
      });

      req.on('end', () => {
        trackingStream.push(null);
      });

      // Process the stream
      await pipeline(
        trackingStream,
        processingPipeline,
        fs.createWriteStream(outputPath)
      );

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Get final file stats
      const stats = await fs.stat(outputPath);
      const compressionRatio = contentLength > 0 
        ? Math.round(((contentLength - stats.size) / contentLength) * 100)
        : 0;

      // Add file info to request
      req.file = {
        fieldname: 'stream',
        originalname: filename,
        encoding: '7bit',
        mimetype: contentType,
        size: stats.size,
        destination: path.dirname(outputPath),
        filename: filename,
        path: outputPath,
        buffer: Buffer.alloc(0), // Not used in streaming
      } as Express.Multer.File;

      // Add processing metadata
      (req.file as any).metadata = {
        originalSize: contentLength,
        compressedSize: stats.size,
        compressionRatio,
        processingTime,
        uploadSpeed: Math.round(contentLength / (processingTime / 1000)), // bytes per second
      };

      console.log(`📤 Streaming upload complete: ${filename}`);
      console.log(`📊 ${(contentLength / 1024 / 1024).toFixed(2)}MB → ${(stats.size / 1024 / 1024).toFixed(2)}MB (${compressionRatio}% reduction)`);
      console.log(`⏱️  Processing time: ${processingTime}ms`);

      next();
    } catch (error) {
      console.error('Streaming upload error:', error);
      next(error);
    }
  };
};

/**
 * Create compression pipeline using Sharp
 */
const createCompressionPipeline = (quality: number) => {
  return sharp()
    .resize(1920, 1080, {
      fit: 'inside',
      withoutEnlargement: true
    })
    .jpeg({
      quality,
      progressive: true,
      mozjpeg: true
    });
};

/**
 * Create passthrough pipeline (no compression)
 */
const createPassthroughPipeline = () => {
  return sharp(); // Just pass through without modification
};

/**
 * Get file extension from MIME type
 */
const getExtensionFromMimeType = (mimeType: string): string => {
  const mimeMap: { [key: string]: string } = {
    'image/jpeg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/webp': '.webp',
  };

  for (const [mime, ext] of Object.entries(mimeMap)) {
    if (mimeType.includes(mime)) {
      return ext;
    }
  }

  return '.jpg'; // Default fallback
};

/**
 * Middleware for handling streaming upload errors
 */
export const handleStreamingUploadError = (error: any, req: Request, res: Response, next: NextFunction) => {
  console.error('Streaming upload error:', error);

  if (error.code === 'EMFILE' || error.code === 'ENFILE') {
    return res.status(503).json({
      success: false,
      message: 'Server is busy. Please try again later.',
    });
  }

  if (error.code === 'ENOSPC') {
    return res.status(507).json({
      success: false,
      message: 'Insufficient storage space.',
    });
  }

  res.status(500).json({
    success: false,
    message: 'Upload failed. Please try again.',
  });
};

/**
 * Memory-efficient bulk upload handler
 */
export const streamingBulkUpload = createStreamingUpload({
  maxFileSize: 50 * 1024 * 1024,
  enableCompression: true,
  compressionQuality: 80,
});

/**
 * High-quality upload for profile pictures
 */
export const streamingProfileUpload = createStreamingUpload({
  maxFileSize: 10 * 1024 * 1024,
  enableCompression: true,
  compressionQuality: 90,
});

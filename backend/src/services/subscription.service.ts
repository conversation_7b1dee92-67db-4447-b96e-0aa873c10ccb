import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';

// Get all subscription plans
export const getAllPlans = async () => {
  return await prisma.subscriptionPlan.findMany({
    where: { isActive: true },
    orderBy: { price: 'asc' },
  });
};

// Get plan by ID
export const getPlanById = async (planId: string) => {
  const plan = await prisma.subscriptionPlan.findUnique({
    where: { id: planId },
  });

  if (!plan) {
    throw new ApiError(404, 'Subscription plan not found');
  }

  return plan;
};

// Get studio's current subscription
export const getStudioSubscription = async (studioId: string) => {
  return await prisma.studioSubscription.findUnique({
    where: { studioId },
    include: {
      plan: true,
      studio: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });
};

// Create subscription for studio
export const createStudioSubscription = async (studioId: string, planId: string) => {
  const plan = await getPlanById(planId);
  
  // Check if studio already has a subscription
  const existingSubscription = await prisma.studioSubscription.findUnique({
    where: { studioId },
  });

  if (existingSubscription) {
    throw new ApiError(400, 'Studio already has an active subscription');
  }

  const now = new Date();
  const trialEnd = plan.trialDays > 0 ? new Date(now.getTime() + plan.trialDays * 24 * 60 * 60 * 1000) : null;
  const periodEnd = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days

  return await prisma.studioSubscription.create({
    data: {
      studioId,
      planId,
      status: plan.trialDays > 0 ? 'TRIAL' : 'ACTIVE',
      currentPeriodStart: now,
      currentPeriodEnd: periodEnd,
      trialEnd,
    },
    include: {
      plan: true,
    },
  });
};

// Update studio subscription
export const updateStudioSubscription = async (studioId: string, planId: string) => {
  const plan = await getPlanById(planId);
  
  const subscription = await prisma.studioSubscription.findUnique({
    where: { studioId },
  });

  if (!subscription) {
    throw new ApiError(404, 'Studio subscription not found');
  }

  const now = new Date();
  const periodEnd = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days

  return await prisma.studioSubscription.update({
    where: { studioId },
    data: {
      planId,
      status: 'ACTIVE',
      currentPeriodStart: now,
      currentPeriodEnd: periodEnd,
      trialEnd: null,
      cancelledAt: null,
    },
    include: {
      plan: true,
    },
  });
};

// Cancel studio subscription
export const cancelStudioSubscription = async (studioId: string) => {
  const subscription = await prisma.studioSubscription.findUnique({
    where: { studioId },
  });

  if (!subscription) {
    throw new ApiError(404, 'Studio subscription not found');
  }

  return await prisma.studioSubscription.update({
    where: { studioId },
    data: {
      status: 'CANCELLED',
      cancelledAt: new Date(),
    },
    include: {
      plan: true,
    },
  });
};

// Check if studio can perform action based on plan limits
export const checkPlanLimits = async (studioId: string, action: string, currentCount?: number) => {
  const subscription = await getStudioSubscription(studioId);
  
  if (!subscription || subscription.status === 'EXPIRED' || subscription.status === 'CANCELLED') {
    throw new ApiError(403, 'No active subscription found');
  }

  const plan = subscription.plan;

  switch (action) {
    case 'create_client':
      if (plan.maxClients > 0 && (currentCount || 0) >= plan.maxClients) {
        throw new ApiError(403, `Plan limit reached. Maximum ${plan.maxClients} clients allowed.`);
      }
      break;
    
    case 'create_user':
      if (plan.maxUsers > 0 && (currentCount || 0) >= plan.maxUsers) {
        throw new ApiError(403, `Plan limit reached. Maximum ${plan.maxUsers} users allowed.`);
      }
      break;
    
    case 'upload_image':
      // Check storage limit
      if (plan.maxStorageGB > 0 && subscription.usedStorageGB >= plan.maxStorageGB) {
        throw new ApiError(403, `Storage limit reached. Maximum ${plan.maxStorageGB}GB allowed.`);
      }
      break;
    
    default:
      break;
  }

  return true;
};

// Update usage statistics
export const updateUsageStats = async (studioId: string) => {
  const subscription = await prisma.studioSubscription.findUnique({
    where: { studioId },
  });

  if (!subscription) {
    return;
  }

  // Get current usage
  const studio = await prisma.studio.findUnique({
    where: { id: studioId },
    include: {
      _count: {
        select: {
          clients: true,
          users: true,
        },
      },
      images: {
        select: {
          size: true,
        },
      },
    },
  });

  if (!studio) {
    return;
  }

  const totalStorageBytes = studio.images.reduce((total, image) => total + image.size, 0);
  const totalStorageGB = totalStorageBytes / (1024 * 1024 * 1024);

  await prisma.studioSubscription.update({
    where: { studioId },
    data: {
      usedClients: studio._count.clients,
      usedUsers: studio._count.users,
      usedStorageGB: totalStorageGB,
    },
  });
};

// Get all studio subscriptions (for admin)
export const getAllStudioSubscriptions = async () => {
  return await prisma.studioSubscription.findMany({
    include: {
      plan: true,
      studio: {
        select: {
          id: true,
          name: true,
          email: true,
          status: true,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });
};

// Get subscription analytics for admin
export const getSubscriptionAnalytics = async () => {
  const totalSubscriptions = await prisma.studioSubscription.count();

  const subscriptionsByPlan = await prisma.studioSubscription.groupBy({
    by: ['planId'],
    _count: {
      id: true,
    },
  });

  const subscriptionsByStatus = await prisma.studioSubscription.groupBy({
    by: ['status'],
    _count: {
      id: true,
    },
  });

  // Get plan details for the grouped data
  const plans = await prisma.subscriptionPlan.findMany();
  const planMap = plans.reduce((acc, plan) => {
    acc[plan.id] = plan;
    return acc;
  }, {} as Record<string, any>);

  const subscriptionsByPlanWithDetails = subscriptionsByPlan.map(item => ({
    plan: planMap[item.planId],
    count: item._count.id,
  }));

  return {
    totalSubscriptions,
    subscriptionsByPlan: subscriptionsByPlanWithDetails,
    subscriptionsByStatus,
  };
};

// Admin: Create subscription plan
export const createPlan = async (planData: any) => {
  // Convert string values to appropriate types
  const processedData = {
    ...planData,
    price: parseFloat(planData.price) || 0,
    maxClients: parseInt(planData.maxClients) || 0,
    maxUsers: parseInt(planData.maxUsers) || 1,
    maxStorageGB: parseInt(planData.maxStorageGB) || 1,
    maxImagesPerClient: parseInt(planData.maxImagesPerClient) || 0,
    trialDays: parseInt(planData.trialDays) || 0,
  };

  return await prisma.subscriptionPlan.create({
    data: processedData,
  });
};

// Admin: Update subscription plan
export const updatePlan = async (planId: string, planData: any) => {
  const plan = await prisma.subscriptionPlan.findUnique({
    where: { id: planId },
  });

  if (!plan) {
    throw new ApiError(404, 'Subscription plan not found');
  }

  // Convert string values to appropriate types
  const processedData = {
    ...planData,
    price: parseFloat(planData.price) || 0,
    maxClients: parseInt(planData.maxClients) || 0,
    maxUsers: parseInt(planData.maxUsers) || 1,
    maxStorageGB: parseInt(planData.maxStorageGB) || 1,
    maxImagesPerClient: parseInt(planData.maxImagesPerClient) || 0,
    trialDays: parseInt(planData.trialDays) || 0,
  };

  return await prisma.subscriptionPlan.update({
    where: { id: planId },
    data: processedData,
  });
};

// Admin: Delete subscription plan
export const deletePlan = async (planId: string) => {
  const plan = await prisma.subscriptionPlan.findUnique({
    where: { id: planId },
  });

  if (!plan) {
    throw new ApiError(404, 'Subscription plan not found');
  }

  // Check if any studios are using this plan
  const subscriptionsUsingPlan = await prisma.studioSubscription.count({
    where: { planId },
  });

  if (subscriptionsUsingPlan > 0) {
    throw new ApiError(400, 'Cannot delete plan that is currently in use by studios');
  }

  await prisma.subscriptionPlan.delete({
    where: { id: planId },
  });
};

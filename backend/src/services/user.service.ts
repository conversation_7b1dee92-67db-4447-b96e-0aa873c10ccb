import bcrypt from 'bcryptjs';
import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';
import { generateToken } from '../middleware/auth.middleware';

// Admin login - Functional
export const loginAdmin = async (email: string, password: string) => {
    const admin = await prisma.admin.findUnique({
      where: { email },
    });

    if (!admin || !(await bcrypt.compare(password, admin.password))) {
      throw new ApiError(401, 'Invalid email or password');
    }

    const token = generateToken({
      id: admin.id,
      role: 'ADMIN',
      email: admin.email,
    });

    return {
      token,
      user: {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: 'ADMIN',
      },
    };
};

// Studio registration - Functional
export const registerStudio = async (data: {
    email: string;
    password: string;
    name: string;
    description?: string;
    phone?: string;
    address?: string;
}) => {
    const existingStudio = await prisma.studio.findUnique({
      where: { email: data.email },
    });

    if (existingStudio) {
      throw new ApiError(400, 'Studio with this email already exists');
    }

    const hashedPassword = await bcrypt.hash(data.password, 12);

    const studio = await prisma.studio.create({
      data: {
        ...data,
        password: hashedPassword,
      },
    });

    return {
      id: studio.id,
      email: studio.email,
      name: studio.name,
      status: studio.status,
      message: 'Studio registered successfully. Waiting for admin approval.',
    };
};

// Studio login - Functional
export const loginStudio = async (email: string, password: string) => {
    const studio = await prisma.studio.findUnique({
      where: { email },
    });

    if (!studio || !(await bcrypt.compare(password, studio.password))) {
      throw new ApiError(401, 'Invalid email or password');
    }

    if (studio.status !== 'APPROVED') {
      throw new ApiError(403, 'Studio account is not approved yet');
    }

    const token = generateToken({
      id: studio.id,
      role: 'STUDIO',
      email: studio.email,
    });

    return {
      token,
      user: {
        id: studio.id,
        email: studio.email,
        name: studio.name,
        role: 'STUDIO',
        status: studio.status,
      },
    };
};

// Get all studios (for admin) - Functional
export const getAllStudios = async () => {
    return await prisma.studio.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        description: true,
        phone: true,
        address: true,
        status: true,
        createdAt: true,
        _count: {
          select: {
            users: true,
            clients: true,
            images: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
};

// Update studio status (approve/reject) - Functional
export const updateStudioStatus = async (studioId: string, status: 'APPROVED' | 'REJECTED') => {
    const studio = await prisma.studio.findUnique({
      where: { id: studioId },
    });

    if (!studio) {
      throw new ApiError(404, 'Studio not found');
    }

    return await prisma.studio.update({
      where: { id: studioId },
      data: { status },
      select: {
        id: true,
        email: true,
        name: true,
        status: true,
      },
    });
};

// Get all users (for admin) - Functional
export const getAllUsers = async () => {
    return await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        phone: true,
        role: true,
        createdAt: true,
        studio: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
};

// Get studio users (for studio dashboard) - Functional
export const getStudioUsers = async (studioId: string) => {
    return await prisma.user.findMany({
      where: { studioId },
      select: {
        id: true,
        email: true,
        name: true,
        phone: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
};

// Get studio by ID (for admin) - Functional
export const getStudioById = async (studioId: string) => {
    const studio = await prisma.studio.findUnique({
      where: { id: studioId },
      select: {
        id: true,
        email: true,
        name: true,
        description: true,
        phone: true,
        address: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            users: true,
            clients: true,
            images: true,
          },
        },
        clients: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            uniqueId: true,
            isActive: true,
            lastLogin: true,
            createdAt: true,
            _count: {
              select: {
                images: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        images: {
          select: {
            size: true,
          },
        },
      },
    });

    if (!studio) {
      throw new ApiError(404, 'Studio not found');
    }

    // Calculate total storage used
    const totalStorageBytes = studio.images.reduce((total, image) => total + image.size, 0);
    const totalStorageMB = Math.round(totalStorageBytes / (1024 * 1024) * 100) / 100;

    return {
      ...studio,
      totalStorageMB,
      images: undefined, // Remove images array from response, we only needed it for calculation
    };
};

// Create studio (for admin) - Functional
export const createStudio = async (data: {
  email: string;
  password: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
  status?: 'PENDING' | 'APPROVED' | 'REJECTED';
}) => {
    const existingStudio = await prisma.studio.findUnique({
      where: { email: data.email },
    });

    if (existingStudio) {
      throw new ApiError(400, 'Studio with this email already exists');
    }

    const hashedPassword = await bcrypt.hash(data.password, 12);

    const studio = await prisma.studio.create({
      data: {
        ...data,
        password: hashedPassword,
        status: data.status || 'PENDING',
      },
      select: {
        id: true,
        email: true,
        name: true,
        description: true,
        phone: true,
        address: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return studio;
};

// Update studio (for admin) - Functional
export const updateStudio = async (studioId: string, data: {
    name?: string;
    email?: string;
    description?: string;
    phone?: string;
    address?: string;
}) => {
    const studio = await prisma.studio.findUnique({
      where: { id: studioId },
    });

    if (!studio) {
      throw new ApiError(404, 'Studio not found');
    }

    // Check if email is being updated and if it already exists
    if (data.email && data.email !== studio.email) {
      const existingStudio = await prisma.studio.findUnique({
        where: { email: data.email },
      });

      if (existingStudio) {
        throw new ApiError(400, 'Studio with this email already exists');
      }
    }

    const updatedStudio = await prisma.studio.update({
      where: { id: studioId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        email: true,
        name: true,
        description: true,
        phone: true,
        address: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            users: true,
          },
        },
      },
    });

    return updatedStudio;
};

// Delete studio (for admin) - Functional
export const deleteStudio = async (studioId: string) => {
    const studio = await prisma.studio.findUnique({
      where: { id: studioId },
      include: {
        _count: {
          select: {
            users: true,
          },
        },
      },
    });

    if (!studio) {
      throw new ApiError(404, 'Studio not found');
    }

    // Check if studio has users
    if (studio._count.users > 0) {
      throw new ApiError(400, 'Cannot delete studio with associated users. Please remove users first.');
    }

    await prisma.studio.delete({
      where: { id: studioId },
    });

    return { message: 'Studio deleted successfully' };
};

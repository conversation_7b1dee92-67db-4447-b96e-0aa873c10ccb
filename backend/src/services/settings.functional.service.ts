import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';
import bcrypt from 'bcryptjs';

// Get or create studio settings
export const getStudioSettings = async (studioId: string) => {
  let settings = await prisma.studioSettings.findUnique({
    where: { studioId },
  });

  // Create default settings if not exists
  if (!settings) {
    settings = await prisma.studioSettings.create({
      data: { studioId },
    });
  }

  return settings;
};

// Update profile settings
export const updateProfile = async (studioId: string, data: {
  profilePicture?: string;
  businessAddress?: string;
  operatingHours?: string;
  socialLinks?: any;
  logoUrl?: string;
}) => {
  const settings = await getStudioSettings(studioId);

  return await prisma.studioSettings.update({
    where: { id: settings.id },
    data,
  });
};

// Update security settings
export const updateSecurity = async (studioId: string, data: {
  twoFactorEnabled?: boolean;
  sessionTimeout?: number;
}) => {
  const settings = await getStudioSettings(studioId);

  // Ensure numeric fields are properly converted
  const updateData = {
    ...data,
    ...(data.sessionTimeout !== undefined && { sessionTimeout: Number(data.sessionTimeout) }),
  };

  return await prisma.studioSettings.update({
    where: { id: settings.id },
    data: updateData,
  });
};

// Update client settings
export const updateClientSettings = async (studioId: string, data: {
  defaultAccessDuration?: number;
  autoGeneratePassword?: boolean;
  emailNotifications?: boolean;
  smsNotifications?: boolean;
}) => {
  const settings = await getStudioSettings(studioId);

  // Ensure numeric fields are properly converted
  const updateData = {
    ...data,
    ...(data.defaultAccessDuration !== undefined && { defaultAccessDuration: Number(data.defaultAccessDuration) }),
  };

  return await prisma.studioSettings.update({
    where: { id: settings.id },
    data: updateData,
  });
};

// Update upload settings
export const updateUploadSettings = async (studioId: string, data: {
  maxFileSize?: number;
  allowedFormats?: string;
  autoResize?: boolean;
  storageQuota?: number;
}) => {
  const settings = await getStudioSettings(studioId);

  // Ensure numeric fields are properly converted
  const updateData = {
    ...data,
    ...(data.maxFileSize !== undefined && { maxFileSize: Number(data.maxFileSize) }),
    ...(data.storageQuota !== undefined && { storageQuota: Number(data.storageQuota) }),
  };

  return await prisma.studioSettings.update({
    where: { id: settings.id },
    data: updateData,
  });
};

// Change password
export const changePassword = async (studioId: string, data: {
  currentPassword: string;
  newPassword: string;
}) => {
  const studio = await prisma.studio.findUnique({
    where: { id: studioId },
  });

  if (!studio) {
    throw new ApiError(404, 'Studio not found');
  }

  // Verify current password
  const isCurrentPasswordValid = await bcrypt.compare(data.currentPassword, studio.password);
  if (!isCurrentPasswordValid) {
    throw new ApiError(400, 'Current password is incorrect');
  }

  // Hash new password
  const hashedNewPassword = await bcrypt.hash(data.newPassword, 12);

  // Update password
  return await prisma.studio.update({
    where: { id: studioId },
    data: { password: hashedNewPassword },
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
    },
  });
};

// Get dashboard stats
export const getDashboardStats = async (studioId: string) => {
  const [totalClients, totalImages, totalStorage] = await Promise.all([
    prisma.client.count({ where: { studioId } }),
    prisma.image.count({ where: { studioId } }),
    prisma.image.aggregate({
      where: { studioId },
      _sum: { size: true },
    }),
  ]);

  return {
    totalClients,
    totalImages,
    totalStorage: totalStorage._sum.size || 0,
  };
};

// Get recent activity
export const getRecentActivity = async (studioId: string) => {
  const recentImages = await prisma.image.findMany({
    where: { studioId },
    orderBy: { uploadedAt: 'desc' },
    take: 10,
    include: {
      client: {
        select: {
          name: true,
          uniqueId: true,
        },
      },
    },
  });

  return recentImages.map(image => ({
    id: image.id,
    type: 'image_upload',
    description: `Image uploaded for ${image.client.name}`,
    timestamp: image.uploadedAt,
    client: image.client,
  }));
};

import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';
import axios from 'axios';
import logger from '../utils/logger';

// FotoOwl-style visitor registration and face matching
export const registerVisitorAndMatch = async (data: {
  eventUniqueId: string;
  name: string;
  phone: string;
  email?: string;
  selfieFile: Express.Multer.File;
  ipAddress?: string;
  userAgent?: string;
}) => {
  try {
    logger.info(`Starting FotoOwl-style registration for visitor ${data.name} in event ${data.eventUniqueId}`);

    // Get event by unique ID
    const event = await prisma.event.findUnique({
      where: { uniqueId: data.eventUniqueId },
      include: {
        images: {
          where: {
            processingStatus: 'completed',
            faceCount: { gt: 0 }
          }
        }
      }
    });

    if (!event) {
      throw new ApiError(404, 'Event not found');
    }

    if (!event.isPublic) {
      throw new ApiError(403, 'This event is not publicly accessible');
    }

    // Check if visitor already registered for this event
    const existingVisitor = await prisma.eventVisitor.findFirst({
      where: {
        eventId: event.id,
        contact: data.phone
      }
    });

    if (existingVisitor) {
      // Return existing visitor's matches
      const matches = await getVisitorMatches(existingVisitor.id);
      return {
        success: true,
        isExisting: true,
        visitor: existingVisitor,
        matches: matches.images,
        message: `Welcome back ${data.name}! Here are your photos.`
      };
    }

    // Call Python service for FotoOwl-style registration
    const pythonServiceUrl = process.env.PYTHON_FACE_SERVICE_URL || 'http://localhost:8000';

    // Use form-data package for Node.js
    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('event_id', event.id);
    formData.append('visitor_name', data.name);
    formData.append('visitor_phone', data.phone);
    formData.append('visitor_email', data.email || '');
    formData.append('selfie', data.selfieFile.buffer, {
      filename: data.selfieFile.originalname,
      contentType: data.selfieFile.mimetype
    });

    const response = await axios.post(
      `${pythonServiceUrl}/api/face/fotoowl-visitor-registration`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
        },
        timeout: 60000, // 60 seconds for processing
      }
    );

    if (!response.data.success) {
      throw new ApiError(400, response.data.error || 'Face processing failed');
    }

    const result = response.data;

    // Note: Skip updating visitor record with additional fields since they don't exist in current schema
    // The visitor record is already created by the Python service

    // Log gallery access (skip if table doesn't exist)
    try {
      await prisma.visitorGalleryAccess.create({
        data: {
          visitorId: result.visitor_id,
          eventId: event.id,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          imagesViewed: result.matched_images?.length || 0
        }
      });
    } catch (galleryError) {
      // Skip gallery access logging if table doesn't exist
      logger.warn('Gallery access logging skipped:', galleryError);
    }

    logger.info(`FotoOwl registration completed for ${data.name}: ${result.matched_images?.length || 0} photos found`);

    return {
      success: true,
      isExisting: false,
      visitor: {
        id: result.visitor_id,
        name: data.name,
        phone: data.phone,
        email: data.email
      },
      matches: result.matched_images || [],
      matchSummary: result.match_summary,
      message: result.message,
      eventInfo: {
        id: event.id,
        name: event.name,
        uniqueId: event.uniqueId,
        totalImages: event.images.length
      }
    };

  } catch (error) {
    logger.error(`Error in FotoOwl visitor registration: ${error}`);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    if (axios.isAxiosError(error)) {
      const message = error.response?.data?.detail || error.message;
      throw new ApiError(500, `Face processing service error: ${message}`);
    }
    
    throw new ApiError(500, 'Failed to register visitor and process face matching');
  }
};

// Get visitor matches from database
export const getVisitorMatches = async (visitorId: string) => {
  try {
    const visitor = await prisma.eventVisitor.findUnique({
      where: { id: visitorId },
      include: {
        matches: {
          include: {
            image: true
          },
          orderBy: {
            confidenceScore: 'desc'
          }
        },
        event: true
      }
    });

    if (!visitor) {
      throw new ApiError(404, 'Visitor not found');
    }

    const images = visitor.matches.map(match => ({
      imageId: match.image.id,
      imagePath: match.image.path,
      filename: match.image.filename,
      confidence: match.confidenceScore,
      faceLocation: match.faceLocation,
      matchedAt: match.matchedAt
    }));

    return {
      success: true,
      visitor: {
        id: visitor.id,
        name: visitor.name,
        phone: visitor.contact,
        email: visitor.email
      },
      event: {
        id: visitor.event.id,
        name: visitor.event.name,
        uniqueId: visitor.event.uniqueId
      },
      totalMatches: images.length,
      images
    };

  } catch (error) {
    logger.error(`Error getting visitor matches: ${error}`);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(500, 'Failed to get visitor matches');
  }
};

// Get event info by unique ID (public access)
export const getEventByUniqueId = async (uniqueId: string) => {
  try {
    const event = await prisma.event.findUnique({
      where: { uniqueId },
      include: {
        studio: {
          select: {
            name: true,
            description: true
          }
        },
        _count: {
          select: {
            images: true,
            visitors: true
          }
        }
      }
    });

    if (!event) {
      throw new ApiError(404, 'Event not found');
    }

    if (!event.isPublic) {
      throw new ApiError(403, 'This event is not publicly accessible');
    }

    return {
      id: event.id,
      name: event.name,
      description: event.description,
      uniqueId: event.uniqueId,
      isPublic: event.isPublic,
      createdAt: event.createdAt,
      studio: event.studio,
      stats: {
        totalImages: event._count.images,
        totalVisitors: event._count.visitors
      }
    };

  } catch (error) {
    logger.error(`Error getting event by unique ID: ${error}`);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(500, 'Failed to get event information');
  }
};

// Update visitor gallery access stats
export const updateGalleryAccess = async (visitorId: string, data: {
  imagesViewed?: number;
  imagesDownloaded?: number;
  sessionDuration?: number;
}) => {
  try {
    const latestAccess = await prisma.visitorGalleryAccess.findFirst({
      where: { visitorId },
      orderBy: { accessedAt: 'desc' }
    });

    if (latestAccess) {
      await prisma.visitorGalleryAccess.update({
        where: { id: latestAccess.id },
        data: {
          imagesViewed: data.imagesViewed || latestAccess.imagesViewed,
          imagesDownloaded: (latestAccess.imagesDownloaded || 0) + (data.imagesDownloaded || 0),
          sessionDuration: data.sessionDuration
        }
      });
    }

    return { success: true };

  } catch (error) {
    logger.error(`Error updating gallery access: ${error}`);
    throw new ApiError(500, 'Failed to update gallery access stats');
  }
};

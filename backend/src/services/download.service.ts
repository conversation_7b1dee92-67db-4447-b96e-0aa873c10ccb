import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';
import path from 'path';
import fs from 'fs/promises';
import archiver from 'archiver';
import { Response } from 'express';

export interface DownloadOptions {
  quality: 'original' | 'optimized' | 'thumbnail';
  format?: 'zip' | 'individual';
  includeMetadata?: boolean;
}

/**
 * Get image download path based on quality preference
 */
export const getImageDownloadPath = async (imageId: string, quality: 'original' | 'optimized' = 'original'): Promise<string> => {
  const image = await prisma.image.findUnique({
    where: { id: imageId }
  });

  if (!image) {
    throw new ApiError(404, 'Image not found');
  }

  const basePath = process.cwd();
  
  if (quality === 'original') {
    // Try to find original version first
    const originalPath = path.join(basePath, 'uploads', 'images', 'originals', image.filename);
    
    try {
      await fs.access(originalPath);
      return originalPath;
    } catch {
      // If original doesn't exist, return optimized version
      console.log(`Original not found for ${image.filename}, serving optimized version`);
      return path.join(basePath, image.path);
    }
  }
  
  // Return optimized version
  return path.join(basePath, image.path);
};

/**
 * Download single image with quality option
 */
export const downloadSingleImage = async (imageId: string, quality: DownloadOptions['quality'] = 'original') => {
  const imagePath = await getImageDownloadPath(imageId, quality);
  
  // Verify file exists
  try {
    await fs.access(imagePath);
    return imagePath;
  } catch {
    throw new ApiError(404, 'Image file not found on disk');
  }
};

/**
 * Download multiple images as ZIP with quality options
 */
export const downloadImagesAsZip = async (
  imageIds: string[], 
  res: Response, 
  options: DownloadOptions = { quality: 'original', format: 'zip' }
) => {
  const archive = archiver('zip', {
    zlib: { level: 9 } // Maximum compression
  });

  // Set response headers
  res.setHeader('Content-Type', 'application/zip');
  res.setHeader('Content-Disposition', `attachment; filename="images-${options.quality}-${Date.now()}.zip"`);

  // Pipe archive to response
  archive.pipe(res);

  let addedCount = 0;
  let totalSize = 0;

  for (const imageId of imageIds) {
    try {
      const image = await prisma.image.findUnique({
        where: { id: imageId }
      });

      if (!image) {
        console.warn(`Image ${imageId} not found in database`);
        continue;
      }

      const imagePath = await getImageDownloadPath(imageId, options.quality);
      
      // Check if file exists
      try {
        const stats = await fs.stat(imagePath);
        totalSize += stats.size;

        // Add file to archive with original name
        archive.file(imagePath, { 
          name: `${image.originalName}`,
          stats: stats
        });

        addedCount++;
        console.log(`Added to ZIP: ${image.originalName} (${options.quality})`);
      } catch (fileError) {
        console.warn(`File not found: ${imagePath}`);
        continue;
      }
    } catch (error) {
      console.error(`Error processing image ${imageId}:`, error);
      continue;
    }
  }

  // Add metadata file if requested
  if (options.includeMetadata) {
    const metadata = {
      downloadDate: new Date().toISOString(),
      quality: options.quality,
      totalImages: addedCount,
      totalSize: totalSize,
      images: await Promise.all(
        imageIds.map(async (id) => {
          const image = await prisma.image.findUnique({ where: { id } });
          return image ? {
            originalName: image.originalName,
            uploadDate: image.uploadedAt,
            size: image.size,
            mimeType: image.mimeType
          } : null;
        })
      ).then(results => results.filter(Boolean))
    };

    archive.append(JSON.stringify(metadata, null, 2), { name: 'metadata.json' });
  }

  console.log(`📦 Creating ZIP with ${addedCount} images (${options.quality} quality)`);
  console.log(`📊 Total size: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);

  // Finalize archive
  await archive.finalize();
};

/**
 * Download all client images with quality options
 */
export const downloadAllClientImages = async (
  clientId: string, 
  res: Response, 
  options: DownloadOptions = { quality: 'original', format: 'zip' }
) => {
  const images = await prisma.image.findMany({
    where: { clientId },
    orderBy: { uploadedAt: 'desc' }
  });

  if (images.length === 0) {
    throw new ApiError(404, 'No images found for this client');
  }

  const imageIds = images.map(img => img.id);
  return downloadImagesAsZip(imageIds, res, options);
};

/**
 * Get download statistics
 */
export const getDownloadStats = async (studioId: string) => {
  const images = await prisma.image.findMany({
    where: { studioId },
    select: {
      size: true,
      originalSize: true,
      path: true
    }
  });

  let totalOptimizedSize = 0;
  let totalOriginalSize = 0;
  let originalFilesCount = 0;

  for (const image of images) {
    totalOptimizedSize += image.size;
    
    // Check if original exists
    const originalPath = path.join(process.cwd(), 'uploads', 'images', 'originals', path.basename(image.path));
    try {
      const stats = await fs.stat(originalPath);
      totalOriginalSize += stats.size;
      originalFilesCount++;
    } catch {
      // Original doesn't exist, use optimized size
      totalOriginalSize += image.size;
    }
  }

  const spaceSaved = totalOriginalSize - totalOptimizedSize;
  const compressionRatio = totalOriginalSize > 0 
    ? Math.round((spaceSaved / totalOriginalSize) * 100)
    : 0;

  return {
    totalImages: images.length,
    originalFilesAvailable: originalFilesCount,
    totalOptimizedSize,
    totalOriginalSize,
    spaceSaved,
    compressionRatio,
    storageEfficiency: `${compressionRatio}% space saved`
  };
};

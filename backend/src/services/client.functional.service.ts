import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { config } from '../config/env';

// Create new client
export const createClient = async (data: {
  name: string;
  email: string;
  phone?: string;
  uniqueId: string;
  password: string;
  studioId: string;
}) => {
  // Check if client with same email or uniqueId exists
  const existingClient = await prisma.client.findFirst({
    where: {
      OR: [
        { email: data.email },
        { uniqueId: data.uniqueId }
      ]
    }
  });

  if (existingClient) {
    throw new ApiError(400, 'Client with this email or unique ID already exists');
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(data.password, 12);

  return await prisma.client.create({
    data: {
      ...data,
      password: hashedPassword,
    },
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      uniqueId: true,
      createdAt: true,
      studioId: true,
    },
  });
};

// Get all clients for a studio
export const getStudioClients = async (studioId: string) => {
  return await prisma.client.findMany({
    where: { studioId },
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      uniqueId: true,
      createdAt: true,
      _count: {
        select: {
          images: true,
        },
      },
    },
    orderBy: { createdAt: 'desc' },
  });
};

// Get client by ID
export const getClientById = async (clientId: string, studioId?: string) => {
  const whereClause: any = { id: clientId };
  if (studioId) {
    whereClause.studioId = studioId;
  }

  const client = await prisma.client.findFirst({
    where: whereClause,
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      uniqueId: true,
      createdAt: true,
      studioId: true,
      _count: {
        select: {
          images: true,
        },
      },
    },
  });

  if (!client) {
    throw new ApiError(404, 'Client not found');
  }

  return client;
};

// Update client
export const updateClient = async (
  clientId: string,
  data: {
    name?: string;
    email?: string;
    phone?: string;
  },
  studioId: string
) => {
  const client = await prisma.client.findFirst({
    where: {
      id: clientId,
      studioId,
    },
  });

  if (!client) {
    throw new ApiError(404, 'Client not found or access denied');
  }

  // Check if email is being updated and already exists
  if (data.email && data.email !== client.email) {
    const existingClient = await prisma.client.findFirst({
      where: {
        email: data.email,
        id: { not: clientId },
      },
    });

    if (existingClient) {
      throw new ApiError(400, 'Client with this email already exists');
    }
  }

  return await prisma.client.update({
    where: { id: clientId },
    data,
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      uniqueId: true,
      createdAt: true,
    },
  });
};

// Delete client
export const deleteClient = async (clientId: string, studioId: string) => {
  const client = await prisma.client.findFirst({
    where: {
      id: clientId,
      studioId,
    },
  });

  if (!client) {
    throw new ApiError(404, 'Client not found or access denied');
  }

  // Delete client (images will be cascade deleted)
  await prisma.client.delete({
    where: { id: clientId },
  });

  return { message: 'Client deleted successfully' };
};

// Client login
export const clientLogin = async (data: {
  uniqueId: string;
  password: string;
}) => {
  const client = await prisma.client.findUnique({
    where: { uniqueId: data.uniqueId },
    include: {
      studio: {
        select: {
          name: true,
        },
      },
    },
  });

  if (!client) {
    throw new ApiError(401, 'Invalid credentials');
  }

  // Verify password
  const isPasswordValid = await bcrypt.compare(data.password, client.password);
  if (!isPasswordValid) {
    throw new ApiError(401, 'Invalid credentials');
  }

  // Generate JWT token
  const token = jwt.sign(
    {
      id: client.id,
      role: 'CLIENT',
      email: client.email,
    },
    config.jwt.secret,
    { expiresIn: config.jwt.expiresIn }
  );

  return {
    token,
    client: {
      id: client.id,
      uniqueId: client.uniqueId,
      name: client.name,
      email: client.email,
      studioName: client.studio.name,
    },
  };
};

// Get client images
export const getClientImages = async (clientId: string) => {
  return await prisma.image.findMany({
    where: { clientId },
    orderBy: { uploadedAt: 'desc' },
  });
};

// Search clients
export const searchClients = async (studioId: string, query: string) => {
  return await prisma.client.findMany({
    where: {
      studioId,
      OR: [
        { name: { contains: query, mode: 'insensitive' } },
        { email: { contains: query, mode: 'insensitive' } },
        { uniqueId: { contains: query, mode: 'insensitive' } },
      ],
    },
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      uniqueId: true,
      createdAt: true,
      _count: {
        select: {
          images: true,
        },
      },
    },
    orderBy: { createdAt: 'desc' },
  });
};

// Get client statistics
export const getClientStats = async (studioId: string) => {
  const [totalClients, clientsWithImages, recentClients] = await Promise.all([
    prisma.client.count({ where: { studioId } }),
    prisma.client.count({
      where: {
        studioId,
        images: {
          some: {},
        },
      },
    }),
    prisma.client.count({
      where: {
        studioId,
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        },
      },
    }),
  ]);

  return {
    totalClients,
    clientsWithImages,
    clientsWithoutImages: totalClients - clientsWithImages,
    recentClients,
  };
};

import { prisma } from '../utils/prisma';
import { ApiError } from '../utils/apiError';
import bcrypt from 'bcryptjs';
import QRCode from 'qrcode';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';

// Generate unique event credentials
const generateEventCredentials = () => {
  const uniqueId = uuidv4().replace(/-/g, '').substring(0, 12).toUpperCase();
  return { uniqueId };
};

// Generate QR code for event access
const generateQRCode = async (accessLink: string): Promise<string> => {
  try {
    const qrCodeDataURL = await QRCode.toDataURL(accessLink, {
      errorCorrectionLevel: 'M',
      margin: 1,
    });
    return qrCodeDataURL;
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new ApiError(500, 'Failed to generate QR code');
  }
};

// Get all events for a studio
export const getStudioEvents = async (studioId: string) => {
  const events = await prisma.event.findMany({
    where: { studioId },
    include: {
      _count: {
        select: {
          images: true,
          visitors: true,
        },
      },
    },
    orderBy: { createdAt: 'desc' },
  });

  return events;
};

// Get event by ID
export const getEventById = async (eventId: string, studioId: string) => {
  const event = await prisma.event.findFirst({
    where: { id: eventId, studioId },
    include: {
      images: {
        include: {
          _count: {
            select: {
              faceDescriptors: true,
            },
          },
        },
      },
      visitors: {
        include: {
          _count: {
            select: {
              matches: true,
            },
          },
        },
      },
      _count: {
        select: {
          images: true,
          visitors: true,
        },
      },
    },
  });

  if (!event) {
    throw new ApiError(404, 'Event not found');
  }

  return event;
};

// Get event by unique ID (for public access)
export const getEventByUniqueId = async (uniqueId: string) => {
  const event = await prisma.event.findUnique({
    where: { uniqueId },
    include: {
      studio: {
        select: {
          name: true,
          description: true,
        },
      },
      _count: {
        select: {
          images: true,
        },
      },
    },
  });

  if (!event) {
    throw new ApiError(404, 'Event not found');
  }

  return event;
};

// Create new event
export const createEvent = async (studioId: string, data: {
  name: string;
  description?: string;
  isPublic?: boolean;
}) => {
  const { uniqueId } = generateEventCredentials();
  const accessLink = `${process.env.CLIENT_PORTAL_URL || 'http://localhost:3002'}/event/${uniqueId}`;
  const qrCode = await generateQRCode(accessLink);

  const event = await prisma.event.create({
    data: {
      uniqueId,
      name: data.name,
      description: data.description,
      isPublic: data.isPublic ?? true,
      accessLink,
      qrCode,
      studioId,
    },
    include: {
      _count: {
        select: {
          images: true,
          visitors: true,
        },
      },
    },
  });

  return event;
};

// Update event
export const updateEvent = async (eventId: string, studioId: string, data: {
  name?: string;
  description?: string;
  isPublic?: boolean;
}) => {
  // Verify event belongs to studio
  const existingEvent = await prisma.event.findFirst({
    where: { id: eventId, studioId },
  });

  if (!existingEvent) {
    throw new ApiError(404, 'Event not found');
  }

  const event = await prisma.event.update({
    where: { id: eventId },
    data: {
      name: data.name,
      description: data.description,
      isPublic: data.isPublic,
      updatedAt: new Date(),
    },
    include: {
      _count: {
        select: {
          images: true,
          visitors: true,
        },
      },
    },
  });

  return event;
};

// Delete event
export const deleteEvent = async (eventId: string, studioId: string) => {
  // Verify event belongs to studio
  const existingEvent = await prisma.event.findFirst({
    where: { id: eventId, studioId },
  });

  if (!existingEvent) {
    throw new ApiError(404, 'Event not found');
  }

  // Delete event (cascade will handle related records)
  await prisma.event.delete({
    where: { id: eventId },
  });

  return { message: 'Event deleted successfully' };
};

// Regenerate QR code for event
export const regenerateEventQR = async (eventId: string, studioId: string) => {
  // Verify event belongs to studio
  const existingEvent = await prisma.event.findFirst({
    where: { id: eventId, studioId },
  });

  if (!existingEvent) {
    throw new ApiError(404, 'Event not found');
  }

  const qrCode = await generateQRCode(existingEvent.accessLink);

  const event = await prisma.event.update({
    where: { id: eventId },
    data: { qrCode },
  });

  return event;
};

// Upload images to event
export const uploadEventImages = async (
  studioId: string,
  eventId: string,
  files: Express.Multer.File[],
  descriptions?: string[]
) => {
  // Verify event belongs to studio
  const event = await prisma.event.findFirst({
    where: { id: eventId, studioId },
  });

  if (!event) {
    throw new ApiError(404, 'Event not found');
  }

  // Prepare batch data for bulk insert
  const imageData = files.map((file, i) => ({
    eventId,
    filename: file.filename,
    originalName: file.originalname,
    path: `uploads/images/${file.filename}`,
    size: file.size,
    mimeType: file.mimetype,
    processingStatus: 'processing',
  }));

  // Bulk insert images
  const images = await prisma.$transaction(
    imageData.map((data) => prisma.eventImage.create({ data }))
  );

  // Call Python service to process faces (FotoOwl-style bulk processing)
  try {
    const imagePaths = images.map(img => img.path);

    console.log(`Starting FotoOwl-style face processing for ${imagePaths.length} images in event ${eventId}`);
    console.log('Image paths:', imagePaths);

    // Verify images exist before sending to Python service
    const fs = require('fs');
    const validPaths = [];
    for (const imagePath of imagePaths) {
      const fullPath = require('path').join(process.cwd(), imagePath);
      if (fs.existsSync(fullPath)) {
        validPaths.push(imagePath);
        console.log(`✅ Image exists: ${fullPath}`);
      } else {
        console.log(`❌ Image missing: ${fullPath}`);
      }
    }

    if (validPaths.length === 0) {
      throw new Error('No valid image files found for processing');
    }

    const result = await callPythonFaceService('process-images', {
      event_id: eventId,
      image_paths: validPaths,  // Send only valid paths
    });

    console.log('Python face processing result:', result);

    // Update image statuses based on Python processing results
    if (result.success && result.images) {
      let completedCount = 0;
      let failedCount = 0;

      for (const processedImage of result.images) {
        const status = processedImage.status === 'success' ? 'completed' : 'failed';

        await prisma.eventImage.updateMany({
          where: {
            eventId: eventId,
            path: processedImage.image_path
          },
          data: {
            processingStatus: status,
            faceCount: processedImage.face_count || 0
          }
        });

        if (status === 'completed') {
          completedCount++;
        } else {
          failedCount++;
        }
      }

      console.log(`FotoOwl processing completed: ${completedCount} successful, ${failedCount} failed`);

      // Create processing job record for tracking
      await prisma.processingJob.create({
        data: {
          eventId: eventId,
          jobType: 'image_processing',
          status: 'completed',
          totalItems: imagePaths.length,
          processedItems: completedCount,
          completedAt: new Date(),
          jobData: {
            totalFaces: result.total_faces || 0,
            processingMethod: 'fotoowl_bulk_upload',
            successRate: (completedCount / imagePaths.length) * 100
          }
        }
      });
    }
  } catch (error) {
    console.error('Error calling Python face service:', error);

    // Update all images to failed status
    await prisma.eventImage.updateMany({
      where: { eventId: eventId },
      data: { processingStatus: 'failed' }
    });

    // Create failed processing job record
    await prisma.processingJob.create({
      data: {
        eventId: eventId,
        jobType: 'image_processing',
        status: 'failed',
        totalItems: images.length,
        processedItems: 0,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        completedAt: new Date(),
        jobData: {
          processingMethod: 'fotoowl_bulk_upload',
          errorType: 'python_service_error'
        }
      }
    });
  }

  return images;
};

// Call Python face recognition service
export const callPythonFaceService = async (endpoint: string, data: any) => {
  const pythonServiceUrl = process.env.PYTHON_FACE_SERVICE_URL || 'http://localhost:8000';
  
  try {
    const response = await axios.post(`${pythonServiceUrl}/api/face/${endpoint}`, data, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 seconds
    });
    
    return response.data;
  } catch (error) {
    console.error(`Error calling Python service ${endpoint}:`, error);
    throw error;
  }
};

// Get event statistics
export const getEventStats = async (eventId: string, studioId: string) => {
  // Verify event belongs to studio
  const event = await prisma.event.findFirst({
    where: { id: eventId, studioId },
  });

  if (!event) {
    throw new ApiError(404, 'Event not found');
  }

  // Get basic stats from database
  const [imageCount, visitorCount, totalFaces] = await Promise.all([
    prisma.eventImage.count({ where: { eventId } }),
    prisma.eventVisitor.count({ where: { eventId } }),
    prisma.eventImage.aggregate({
      where: { eventId },
      _sum: { faceCount: true },
    }),
  ]);

  // Try to get additional stats from Python service
  let pythonStats = null;
  try {
    const pythonServiceUrl = process.env.PYTHON_FACE_SERVICE_URL || 'http://localhost:8000';
    const response = await axios.get(`${pythonServiceUrl}/api/face/event/${eventId}/stats`, {
      timeout: 10000, // 10 seconds
    });
    pythonStats = response.data;
  } catch (error) {
    console.error('Error getting Python stats:', error);
  }

  return {
    eventId,
    totalImages: imageCount,
    totalVisitors: visitorCount,
    totalFaces: totalFaces._sum.faceCount || 0,
    pythonStats,
  };
};

// Reprocess event images
export const reprocessEventImages = async (eventId: string, studioId: string) => {
  // Verify event belongs to studio
  const event = await prisma.event.findFirst({
    where: { id: eventId, studioId },
    include: { images: true }
  });

  if (!event) {
    throw new Error('Event not found');
  }

  const imagePaths = event.images.map(img => img.path);

  if (imagePaths.length === 0) {
    throw new Error('No images to process');
  }

  // Update all images to processing status
  await prisma.eventImage.updateMany({
    where: { eventId },
    data: { processingStatus: 'processing' }
  });

  try {
    const result = await callPythonFaceService('process-images', {
      event_id: eventId,
      image_paths: imagePaths
    });

    console.log('Python reprocessing result:', result);

    // Update statuses based on results
    if (result.success && result.images) {
      for (const processedImage of result.images) {
        await prisma.eventImage.updateMany({
          where: {
            eventId: eventId,
            path: processedImage.image_path
          },
          data: {
            processingStatus: processedImage.status === 'success' ? 'completed' : 'failed',
            faceCount: processedImage.face_count || 0
          }
        });
      }
      console.log(`Updated ${result.images.length} image statuses after reprocessing`);
    }

    return result;
  } catch (error) {
    console.error('Error in reprocessing:', error);
    // Update all to failed
    await prisma.eventImage.updateMany({
      where: { eventId },
      data: { processingStatus: 'failed' }
    });
    throw error;
  }
};

import Joi from 'joi';

export const updateProfileSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional().messages({
    'string.min': 'Studio name must be at least 2 characters long',
    'string.max': 'Studio name cannot exceed 100 characters',
  }),
  description: Joi.string().max(500).optional().allow('').messages({
    'string.max': 'Description cannot exceed 500 characters',
  }),
  phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-()]{7,15}$/).optional().allow('').messages({
    'string.pattern.base': 'Please provide a valid phone number',
  }),
  address: Joi.string().max(200).optional().allow('').messages({
    'string.max': 'Address cannot exceed 200 characters',
  }),
});

export const updateProfileSettingsSchema = Joi.object({
  profilePicture: Joi.string().uri().optional().allow('').messages({
    'string.uri': 'Profile picture must be a valid URL',
  }),
  businessAddress: Joi.string().max(300).optional().allow('').messages({
    'string.max': 'Business address cannot exceed 300 characters',
  }),
  operatingHours: Joi.string().max(200).optional().allow('').messages({
    'string.max': 'Operating hours cannot exceed 200 characters',
  }),
  socialLinks: Joi.object().optional(),
  logoUrl: Joi.string().uri().optional().allow('').messages({
    'string.uri': 'Logo URL must be a valid URL',
  }),
});

export const updateSecuritySettingsSchema = Joi.object({
  twoFactorEnabled: Joi.boolean().optional(),
  sessionTimeout: Joi.number().min(5).max(480).optional().messages({
    'number.min': 'Session timeout must be at least 5 minutes',
    'number.max': 'Session timeout cannot exceed 480 minutes (8 hours)',
  }),
});

export const updateClientSettingsSchema = Joi.object({
  defaultAccessDuration: Joi.number().min(1).max(365).optional().messages({
    'number.min': 'Access duration must be at least 1 day',
    'number.max': 'Access duration cannot exceed 365 days',
  }),
  autoGeneratePassword: Joi.boolean().optional(),
  emailNotifications: Joi.boolean().optional(),
  smsNotifications: Joi.boolean().optional(),
});

export const updateUploadSettingsSchema = Joi.object({
  maxFileSize: Joi.number().min(1).max(100).optional().messages({
    'number.min': 'Maximum file size must be at least 1 MB',
    'number.max': 'Maximum file size cannot exceed 100 MB',
  }),
  allowedFormats: Joi.string().optional().messages({
    'string.base': 'Allowed formats must be a string',
  }),
  autoResize: Joi.boolean().optional(),
  storageQuota: Joi.number().min(100).max(10000).optional().messages({
    'number.min': 'Storage quota must be at least 100 MB',
    'number.max': 'Storage quota cannot exceed 10000 MB (10 GB)',
  }),
});

export const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required().messages({
    'any.required': 'Current password is required',
  }),
  newPassword: Joi.string().min(6).required().messages({
    'string.min': 'New password must be at least 6 characters long',
    'any.required': 'New password is required',
  }),
  confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required().messages({
    'any.only': 'Confirm password must match new password',
    'any.required': 'Confirm password is required',
  }),
});

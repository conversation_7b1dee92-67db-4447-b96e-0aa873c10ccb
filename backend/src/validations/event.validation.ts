import Joi from 'joi';

export const createEventSchema = Joi.object({
  name: Joi.string().min(1).max(100).required().messages({
    'string.empty': 'Event name is required',
    'string.min': 'Event name must be at least 1 character long',
    'string.max': 'Event name cannot exceed 100 characters',
    'any.required': 'Event name is required',
  }),
  description: Joi.string().max(500).optional().allow('').messages({
    'string.max': 'Description cannot exceed 500 characters',
  }),
  isPublic: Joi.boolean().optional().messages({
    'boolean.base': 'isPublic must be a boolean value',
  }),
});

export const updateEventSchema = Joi.object({
  name: Joi.string().min(1).max(100).optional().messages({
    'string.empty': 'Event name cannot be empty',
    'string.min': 'Event name must be at least 1 character long',
    'string.max': 'Event name cannot exceed 100 characters',
  }),
  description: Joi.string().max(500).optional().allow('').messages({
    'string.max': 'Description cannot exceed 500 characters',
  }),
  isPublic: Joi.boolean().optional().messages({
    'boolean.base': 'isPublic must be a boolean value',
  }),
});

export const bulkUploadEventImagesSchema = Joi.object({
  eventId: Joi.string().required().messages({
    'any.required': 'Event ID is required',
  }),
  descriptions: Joi.array().items(Joi.string().allow('')).optional(),
});

export const eventIdParamSchema = Joi.object({
  eventId: Joi.string().required().messages({
    'any.required': 'Event ID is required',
  }),
});

export const uniqueIdParamSchema = Joi.object({
  uniqueId: Joi.string().required().messages({
    'any.required': 'Unique ID is required',
  }),
});

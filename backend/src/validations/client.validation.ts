import Joi from 'joi';

export const createClientSchema = Joi.object({
  name: Joi.string().min(2).max(100).required().messages({
    'string.min': 'Client name must be at least 2 characters long',
    'string.max': 'Client name cannot exceed 100 characters',
    'any.required': 'Client name is required',
  }),
  email: Joi.string().email().optional().allow('').messages({
    'string.email': 'Please provide a valid email address',
  }),
  phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-()]{7,15}$/).optional().allow('').messages({
    'string.pattern.base': 'Please provide a valid phone number',
  }),
});

export const updateClientSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional().messages({
    'string.min': 'Client name must be at least 2 characters long',
    'string.max': 'Client name cannot exceed 100 characters',
  }),
  email: Joi.string().email().optional().allow('').messages({
    'string.email': 'Please provide a valid email address',
  }),
  phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-()]{7,15}$/).optional().allow('').messages({
    'string.pattern.base': 'Please provide a valid phone number',
  }),
  isActive: Joi.boolean().optional(),
});

export const clientLoginSchema = Joi.object({
  uniqueId: Joi.string().required().messages({
    'any.required': 'Client ID is required',
  }),
  password: Joi.string().required().messages({
    'any.required': 'Password is required',
  }),
});

export const bulkUploadSchema = Joi.object({
  clientId: Joi.string().required().messages({
    'any.required': 'Client ID is required',
  }),
  descriptions: Joi.array().items(Joi.string().allow('')).optional(),
});

export const updateImageSchema = Joi.object({
  description: Joi.string().max(500).optional().allow('').messages({
    'string.max': 'Description cannot exceed 500 characters',
  }),
  tags: Joi.string().max(200).optional().allow('').messages({
    'string.max': 'Tags cannot exceed 200 characters',
  }),
});

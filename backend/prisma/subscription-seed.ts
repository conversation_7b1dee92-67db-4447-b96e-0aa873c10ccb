import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const subscriptionPlans = [
  {
    name: 'Free Starter',
    type: 'FREE',
    price: 0,
    currency: 'USD',
    maxClients: 5,
    maxUsers: 1,
    maxStorageGB: 1,
    maxImagesPerClient: 50,
    customBranding: false,
    advancedAnalytics: false,
    prioritySupport: false,
    apiAccess: false,
    whiteLabel: false,
    customDomain: false,
    billingCycle: 'monthly',
    trialDays: 0,
    isActive: true,
  },
  {
    name: 'Basic Professional',
    type: 'BASIC',
    price: 29.99,
    currency: 'USD',
    maxClients: 50,
    maxUsers: 2,
    maxStorageGB: 10,
    maxImagesPerClient: 500,
    customBranding: true,
    advancedAnalytics: false,
    prioritySupport: false,
    apiAccess: false,
    whiteLabel: false,
    customDomain: false,
    billingCycle: 'monthly',
    trialDays: 14,
    isActive: true,
  },
  {
    name: 'Premium Studio',
    type: 'PREMIUM',
    price: 79.99,
    currency: 'USD',
    maxClients: 200,
    maxUsers: 5,
    maxStorageGB: 50,
    maxImagesPerClient: 0, // unlimited
    customBranding: true,
    advancedAnalytics: true,
    prioritySupport: true,
    apiAccess: true,
    whiteLabel: false,
    customDomain: true,
    billingCycle: 'monthly',
    trialDays: 30,
    isActive: true,
  },
  {
    name: 'Enterprise Solution',
    type: 'ENTERPRISE',
    price: 199.99,
    currency: 'USD',
    maxClients: 0, // unlimited
    maxUsers: 0, // unlimited
    maxStorageGB: 500,
    maxImagesPerClient: 0, // unlimited
    customBranding: true,
    advancedAnalytics: true,
    prioritySupport: true,
    apiAccess: true,
    whiteLabel: true,
    customDomain: true,
    billingCycle: 'monthly',
    trialDays: 30,
    isActive: true,
  },
];

export async function seedSubscriptionPlans() {
  console.log('🔄 Seeding subscription plans...');

  for (const planData of subscriptionPlans) {
    await prisma.subscriptionPlan.upsert({
      where: { name: planData.name },
      update: planData as any,
      create: planData as any,
    });
    console.log(`✅ Created/Updated plan: ${planData.name}`);
  }

  console.log('🎉 Subscription plans seeded successfully!');
}

// Run if called directly
if (require.main === module) {
  seedSubscriptionPlans()
    .catch((e) => {
      console.error('❌ Error seeding subscription plans:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  STUDIO
  USER
}

enum StudioStatus {
  PENDING
  APPROVED
  REJECTED
}

enum PlanType {
  FREE
  BASIC
  PREMIUM
  ENTERPRISE
}

enum SubscriptionStatus {
  ACTIVE
  EXPIRED
  CANCELLED
  TRIAL
}

model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admins")
}

model Studio {
  id          String        @id @default(cuid())
  email       String        @unique
  password    String
  name        String
  description String?
  phone       String?
  address     String?
  status      StudioStatus  @default(PENDING)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  users        User[]
  clients      Client[]
  images       Image[]
  events       Event[]
  settings     StudioSettings?
  subscription StudioSubscription?

  @@map("studios")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  phone     String?
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  studioId  String?
  studio    Studio?  @relation(fields: [studioId], references: [id], onDelete: SetNull)

  @@map("users")
}

model Client {
  id          String    @id @default(cuid())
  uniqueId    String    @unique
  name        String
  email       String?
  phone       String?
  password    String
  qrCode      String?
  accessLink  String
  isActive    Boolean   @default(true)
  lastLogin   DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  studioId    String
  studio      Studio    @relation(fields: [studioId], references: [id], onDelete: Cascade)
  images      Image[]

  @@map("clients")
}

model Image {
  id           String   @id @default(cuid())
  filename     String
  originalName String
  path         String
  size         Int
  mimeType     String
  description  String?
  tags         String?
  uploadedAt   DateTime @default(now())

  // Relations
  clientId     String
  client       Client   @relation(fields: [clientId], references: [id], onDelete: Cascade)
  studioId     String
  studio       Studio   @relation(fields: [studioId], references: [id], onDelete: Cascade)

  @@map("images")
}

model StudioSettings {
  id        String   @id @default(cuid())
  studioId  String   @unique

  // Profile Settings
  profilePicture    String?
  businessAddress   String?
  operatingHours    String?
  socialLinks       Json?
  logoUrl          String?

  // Security Settings
  twoFactorEnabled Boolean @default(false)
  sessionTimeout   Int     @default(30) // minutes

  // Client Settings
  defaultAccessDuration Int     @default(30) // days
  autoGeneratePassword  Boolean @default(true)
  emailNotifications    Boolean @default(true)
  smsNotifications      Boolean @default(false)

  // Upload Settings
  maxFileSize          Int     @default(10) // MB
  allowedFormats       String  @default("jpg,jpeg,png,gif")
  autoResize           Boolean @default(true)
  storageQuota         Int     @default(1000) // MB

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  studio    Studio   @relation(fields: [studioId], references: [id], onDelete: Cascade)

  @@map("studio_settings")
}

model SubscriptionPlan {
  id          String   @id @default(cuid())
  name        String   @unique
  type        PlanType
  price       Float    @default(0)
  currency    String   @default("USD")

  // Features
  maxClients      Int     @default(0) // 0 = unlimited
  maxUsers        Int     @default(1)
  maxStorageGB    Int     @default(1)
  maxImagesPerClient Int  @default(0) // 0 = unlimited

  // Feature flags
  customBranding     Boolean @default(false)
  advancedAnalytics  Boolean @default(false)
  prioritySupport    Boolean @default(false)
  apiAccess          Boolean @default(false)
  whiteLabel         Boolean @default(false)
  customDomain       Boolean @default(false)

  // Billing
  billingCycle    String  @default("monthly") // monthly, yearly
  trialDays       Int     @default(0)

  isActive        Boolean @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  subscriptions   StudioSubscription[]

  @@map("subscription_plans")
}

model StudioSubscription {
  id              String             @id @default(cuid())
  studioId        String
  planId          String
  status          SubscriptionStatus @default(TRIAL)

  // Billing
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  trialEnd           DateTime?
  cancelledAt        DateTime?

  // Usage tracking
  usedClients     Int @default(0)
  usedUsers       Int @default(0)
  usedStorageGB   Float @default(0)

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  studio          Studio           @relation(fields: [studioId], references: [id], onDelete: Cascade)
  plan            SubscriptionPlan @relation(fields: [planId], references: [id])

  @@unique([studioId])
  @@map("studio_subscriptions")
}

// Face Recognition Models

model Event {
  id          String   @id @default(cuid())
  studioId    String
  name        String
  description String?
  isPublic    Boolean  @default(true)
  uniqueId    String   @unique
  accessLink  String
  qrCode      String?

  // Event settings
  eventDate   DateTime?
  location    String?
  eventType   String?  // wedding, party, corporate, etc.

  // Processing settings - Production Ready
  faceMatchThreshold Float @default(0.9) // Euclidean distance threshold for RetinaFace + ArcFace
  autoProcessing     Boolean @default(true)
  qualityThreshold   Float @default(0.7) // Minimum face quality score

  // Status tracking
  status      String   @default("active") // active, processing, completed, archived
  totalImages Int      @default(0)
  totalFaces  Int      @default(0)
  totalVisitors Int    @default(0)

  // Performance metrics
  avgProcessingTime Float? // Average processing time per image (seconds)
  lastProcessedAt   DateTime?

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  studio      Studio        @relation(fields: [studioId], references: [id], onDelete: Cascade)
  images      EventImage[]
  visitors    EventVisitor[]
  galleryAccess VisitorGalleryAccess[]
  processingJobs ProcessingJob[] @relation("EventProcessingJobs")

  @@index([studioId, status])
  @@index([status, createdAt])
  @@map("events")
}

model EventImage {
  id               String   @id @default(cuid())
  eventId          String
  filename         String
  originalName     String
  path             String
  thumbnailPath    String?  // Optimized thumbnail for fast loading
  size             Int
  width            Int?
  height           Int?
  mimeType         String
  checksum         String?  // SHA-256 hash for integrity verification

  // Face processing status
  faceCount        Int      @default(0)
  processingStatus String   @default("pending") // pending, processing, completed, failed, skipped
  processedAt      DateTime?
  processingError  String?  // Detailed error message if processing failed
  retryCount       Int      @default(0)
  maxRetries       Int      @default(3)

  // Image quality metrics for production
  qualityScore     Float?   // Overall image quality (0-1)
  blurScore        Float?   // Blur detection score (0-1, higher = less blur)
  brightnessScore  Float?   // Brightness level (0-1)
  contrastScore    Float?   // Contrast level (0-1)

  // Processing performance
  processingTimeMs Int?     // Processing time in milliseconds
  detectionModel   String?  // Model used for face detection (RetinaFace, MTCNN, etc.)

  uploadedAt       DateTime @default(now())

  // Relations
  event            Event            @relation(fields: [eventId], references: [id], onDelete: Cascade)
  faceDescriptors  FaceDescriptor[]
  visitorMatches   VisitorMatch[]

  @@index([eventId, processingStatus])
  @@index([processingStatus, uploadedAt])
  @@index([eventId, faceCount])
  @@map("event_images")
}

model FaceDescriptor {
  id               String  @id @default(cuid())
  imageId          String
  descriptorVector Float[] // 512-dimensional ArcFace encoding for production

  // Face detection data
  boundingBox      Json    // {x, y, width, height, confidence} from RetinaFace
  confidence       Float   @default(0.0) // Detection confidence
  landmarks        Json?   // 68-point facial landmarks

  // Face quality metrics - Critical for production matching
  qualityScore     Float?  // Overall face quality (0-1)
  sharpness        Float?  // Face sharpness score
  brightness       Float?  // Face brightness level
  pose             Json?   // Head pose {yaw, pitch, roll} in degrees

  // Face attributes (optional for analytics)
  age              Int?    // Estimated age
  gender           String? // Estimated gender
  emotion          String? // Dominant emotion

  // Processing metadata
  detectionModel   String  @default("RetinaFace") // Face detection model
  encodingModel    String  @default("ArcFace")    // Face encoding model
  processingTimeMs Int?    // Time taken to process this face

  createdAt        DateTime @default(now())

  // Relations
  image            EventImage     @relation(fields: [imageId], references: [id], onDelete: Cascade)
  matches          VisitorMatch[]

  @@index([imageId])
  @@index([qualityScore])
  @@map("face_descriptors")
}

model EventVisitor {
  id                String   @id @default(cuid())
  eventId           String
  name              String
  contact           String?  // Phone number
  email             String?

  // Selfie data - Production ready
  selfiePath        String
  selfieDescriptor  Float[]  // 512-dimensional ArcFace encoding
  selfieThumbnail   String?  // Optimized thumbnail path
  selfieQuality     Float?   // Quality score of uploaded selfie (0-1)
  selfieSize        Int?     // File size in bytes
  selfieChecksum    String?  // SHA-256 hash for integrity

  // Processing status with detailed tracking
  processingStatus  String   @default("pending") // pending, processing, completed, failed
  processingError   String?  // Detailed error message
  retryCount        Int      @default(0)
  processingTimeMs  Int?     // Time taken to process selfie

  // Registration workflow - FotoOwl style
  registrationStep  String   @default("registered") // registered, selfie_uploaded, processing, completed

  // Device and location tracking
  ipAddress         String?
  userAgent         String?
  deviceInfo        Json?    // Device fingerprint
  location          Json?    // Geolocation if available
  referrer          String?  // How they found the event

  // Match statistics for analytics
  totalMatches      Int      @default(0)
  bestMatchScore    Float?   // Best similarity score achieved
  averageMatchScore Float?   // Average similarity across all matches
  matchesAboveThreshold Int  @default(0) // Count of high-confidence matches

  // Access tracking
  visitedAt         DateTime @default(now())
  lastActiveAt      DateTime @default(now())
  totalSessions     Int      @default(1)

  // Relations
  event             Event          @relation(fields: [eventId], references: [id], onDelete: Cascade)
  matches           VisitorMatch[]
  galleryAccess     VisitorGalleryAccess[]

  @@index([eventId, processingStatus])
  @@index([eventId, registrationStep])
  @@index([processingStatus, visitedAt])
  @@map("event_visitors")
}

model VisitorMatch {
  id                String   @id @default(cuid())
  visitorId         String
  imageId           String
  faceDescriptorId  String

  // Core matching metrics - Production ready
  euclideanDistance Float    // Euclidean distance between 512-d vectors
  similarity        Float    // Cosine similarity score (0-1)
  confidenceScore   Float    // Overall match confidence
  matchThreshold    Float    @default(0.9) // Threshold used for this match

  // Match quality indicators
  qualityScore      Float?   // Combined quality score
  faceQualityScore  Float?   // Quality of the matched face
  selfieQualityScore Float?  // Quality of visitor's selfie

  // Geometric verification
  faceLocation      Json     // Bounding box of matched face {x, y, width, height}
  faceSize          Float?   // Relative face size in image
  poseAlignment     Float?   // How well poses align (0-1)

  // Processing metadata
  matchingModel     String   @default("ArcFace") // Model used for matching
  processingTimeMs  Int?     // Time taken to compute this match

  // Verification and quality control
  isVerified        Boolean  @default(false) // Manual verification
  verifiedBy        String?  // Admin/Studio user who verified
  verifiedAt        DateTime?
  isAutoApproved    Boolean  @default(false) // Auto-approved based on high confidence

  // Analytics
  viewCount         Int      @default(0) // How many times visitor viewed this match
  downloadCount     Int      @default(0) // How many times downloaded

  matchedAt         DateTime @default(now())

  // Relations
  visitor           EventVisitor   @relation(fields: [visitorId], references: [id], onDelete: Cascade)
  image             EventImage     @relation(fields: [imageId], references: [id], onDelete: Cascade)
  faceDescriptor    FaceDescriptor @relation(fields: [faceDescriptorId], references: [id], onDelete: Cascade)

  @@unique([visitorId, imageId, faceDescriptorId])
  @@index([visitorId, confidenceScore])
  @@index([euclideanDistance])
  @@index([similarity])
  @@map("visitor_matches")
}

model ProcessingJob {
  id              String    @id @default(cuid())
  eventId         String
  jobType         String    // "image_processing", "face_matching", "visitor_processing", "batch_upload"
  status          String    @default("pending") // pending, processing, completed, failed, cancelled

  // Progress tracking
  totalItems      Int       @default(0)
  processedItems  Int       @default(0)
  failedItems     Int       @default(0)
  skippedItems    Int       @default(0)

  // Error handling
  errorMessage    String?
  errorDetails    Json?     // Detailed error information

  // Timing
  startedAt       DateTime  @default(now())
  completedAt     DateTime?
  estimatedTimeRemaining Int? // Seconds

  // Job configuration
  jobData         Json?     // Job-specific configuration
  priority        Int       @default(0) // Higher = more priority
  retryCount      Int       @default(0)
  maxRetries      Int       @default(3)

  // Performance metrics
  avgProcessingTimePerItem Float? // Average time per item in seconds
  throughputPerSecond     Float?  // Items processed per second

  // Worker information
  workerId        String?   // ID of worker processing this job
  workerVersion   String?   // Version of processing software

  // Relations
  event           Event     @relation("EventProcessingJobs", fields: [eventId], references: [id], onDelete: Cascade)

  @@index([status, priority, startedAt])
  @@index([eventId, status])
  @@index([jobType, status])
  @@map("processing_jobs")
}

// Production-ready visitor gallery access tracking
model VisitorGalleryAccess {
  id              String    @id @default(cuid())
  visitorId       String
  eventId         String

  // Session tracking
  sessionId       String    // Unique session identifier
  accessedAt      DateTime  @default(now())
  sessionEndedAt  DateTime?
  sessionDuration Int?      // Total session duration in seconds

  // Device and location
  ipAddress       String?
  userAgent       String?
  deviceType      String?   // mobile, desktop, tablet
  browser         String?   // chrome, safari, firefox, etc.
  location        Json?     // Geolocation data

  // Activity metrics
  imagesViewed    Int       @default(0)
  imagesDownloaded Int      @default(0)
  totalClicks     Int       @default(0)
  scrollDepth     Float?    // How far they scrolled (0-1)

  // Performance metrics
  pageLoadTime    Int?      // Page load time in milliseconds
  averageImageLoadTime Int? // Average image load time

  // Engagement
  timeOnGallery   Int?      // Time spent viewing gallery (seconds)
  returnVisit     Boolean   @default(false) // Is this a return visit?

  // Relations
  visitor         EventVisitor @relation(fields: [visitorId], references: [id], onDelete: Cascade)
  event           Event        @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@index([visitorId, accessedAt])
  @@index([eventId, accessedAt])
  @@index([sessionId])
  @@map("visitor_gallery_access")
}

// System monitoring and analytics
model SystemMetrics {
  id                    String    @id @default(cuid())
  metricType           String    // "processing_performance", "api_response_time", "error_rate", etc.
  metricName           String    // Specific metric name
  value                Float     // Metric value
  unit                 String?   // Unit of measurement (ms, %, count, etc.)

  // Context
  eventId              String?   // Associated event if applicable
  studioId             String?   // Associated studio if applicable

  // Metadata
  metadata             Json?     // Additional metric data
  tags                 Json?     // Metric tags for filtering

  recordedAt           DateTime  @default(now())

  @@index([metricType, metricName, recordedAt])
  @@index([eventId, metricType])
  @@map("system_metrics")
}

// Error logging for production monitoring
model ErrorLog {
  id              String    @id @default(cuid())
  errorType       String    // "face_detection_error", "matching_error", "upload_error", etc.
  errorMessage    String
  errorStack      String?   // Stack trace

  // Context
  eventId         String?
  visitorId       String?
  imageId         String?
  userId          String?   // Studio user if applicable

  // Request context
  requestId       String?   // Unique request identifier
  endpoint        String?   // API endpoint where error occurred
  httpMethod      String?   // GET, POST, etc.
  userAgent       String?
  ipAddress       String?

  // Error details
  severity        String    @default("error") // info, warning, error, critical
  resolved        Boolean   @default(false)
  resolvedAt      DateTime?
  resolvedBy      String?

  // Metadata
  errorData       Json?     // Additional error context

  occurredAt      DateTime  @default(now())

  @@index([errorType, severity, occurredAt])
  @@index([eventId, errorType])
  @@index([resolved, severity])
  @@map("error_logs")
}

// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  STUDIO
  USER
}

enum StudioStatus {
  PENDING
  APPROVED
  REJECTED
}

enum PlanType {
  FREE
  BASIC
  PREMIUM
  ENTERPRISE
}

enum SubscriptionStatus {
  ACTIVE
  EXPIRED
  CANCELLED
  TRIAL
}

model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admins")
}

model Studio {
  id          String        @id @default(cuid())
  email       String        @unique
  password    String
  name        String
  description String?
  phone       String?
  address     String?
  status      StudioStatus  @default(PENDING)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  users        User[]
  clients      Client[]
  images       Image[]
  events       Event[]
  settings     StudioSettings?
  subscription StudioSubscription?

  @@map("studios")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  phone     String?
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  studioId  String?
  studio    Studio?  @relation(fields: [studioId], references: [id], onDelete: SetNull)

  @@map("users")
}

model Client {
  id          String    @id @default(cuid())
  uniqueId    String    @unique
  name        String
  email       String?
  phone       String?
  password    String
  qrCode      String?
  accessLink  String
  isActive    Boolean   @default(true)
  lastLogin   DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  studioId    String
  studio      Studio    @relation(fields: [studioId], references: [id], onDelete: Cascade)
  images      Image[]

  @@map("clients")
}

model Image {
  id           String   @id @default(cuid())
  filename     String
  originalName String
  path         String
  size         Int
  mimeType     String
  description  String?
  tags         String?
  uploadedAt   DateTime @default(now())

  // Relations
  clientId     String
  client       Client   @relation(fields: [clientId], references: [id], onDelete: Cascade)
  studioId     String
  studio       Studio   @relation(fields: [studioId], references: [id], onDelete: Cascade)

  @@map("images")
}

model StudioSettings {
  id        String   @id @default(cuid())
  studioId  String   @unique

  // Profile Settings
  profilePicture    String?
  businessAddress   String?
  operatingHours    String?
  socialLinks       Json?
  logoUrl          String?

  // Security Settings
  twoFactorEnabled Boolean @default(false)
  sessionTimeout   Int     @default(30) // minutes

  // Client Settings
  defaultAccessDuration Int     @default(30) // days
  autoGeneratePassword  Boolean @default(true)
  emailNotifications    Boolean @default(true)
  smsNotifications      Boolean @default(false)

  // Upload Settings
  maxFileSize          Int     @default(10) // MB
  allowedFormats       String  @default("jpg,jpeg,png,gif")
  autoResize           Boolean @default(true)
  storageQuota         Int     @default(1000) // MB

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  studio    Studio   @relation(fields: [studioId], references: [id], onDelete: Cascade)

  @@map("studio_settings")
}

model SubscriptionPlan {
  id          String   @id @default(cuid())
  name        String   @unique
  type        PlanType
  price       Float    @default(0)
  currency    String   @default("USD")

  // Features
  maxClients      Int     @default(0) // 0 = unlimited
  maxUsers        Int     @default(1)
  maxStorageGB    Int     @default(1)
  maxImagesPerClient Int  @default(0) // 0 = unlimited

  // Feature flags
  customBranding     Boolean @default(false)
  advancedAnalytics  Boolean @default(false)
  prioritySupport    Boolean @default(false)
  apiAccess          Boolean @default(false)
  whiteLabel         Boolean @default(false)
  customDomain       Boolean @default(false)

  // Billing
  billingCycle    String  @default("monthly") // monthly, yearly
  trialDays       Int     @default(0)

  isActive        Boolean @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  subscriptions   StudioSubscription[]

  @@map("subscription_plans")
}

model StudioSubscription {
  id              String             @id @default(cuid())
  studioId        String
  planId          String
  status          SubscriptionStatus @default(TRIAL)

  // Billing
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  trialEnd           DateTime?
  cancelledAt        DateTime?

  // Usage tracking
  usedClients     Int @default(0)
  usedUsers       Int @default(0)
  usedStorageGB   Float @default(0)

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  studio          Studio           @relation(fields: [studioId], references: [id], onDelete: Cascade)
  plan            SubscriptionPlan @relation(fields: [planId], references: [id])

  @@unique([studioId])
  @@map("studio_subscriptions")
}

// Face Recognition Models

model Event {
  id          String   @id @default(cuid())
  studioId    String
  name        String
  description String?
  isPublic    Boolean  @default(true)
  uniqueId    String   @unique
  accessLink  String
  qrCode      String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  studio      Studio        @relation(fields: [studioId], references: [id], onDelete: Cascade)
  images      EventImage[]
  visitors    EventVisitor[]
  galleryAccess VisitorGalleryAccess[]

  @@map("events")
}

model EventImage {
  id               String   @id @default(cuid())
  eventId          String
  filename         String
  originalName     String
  path             String
  size             Int
  mimeType         String
  faceCount        Int      @default(0)
  processingStatus String   @default("pending") // pending, processing, completed, failed
  uploadedAt       DateTime @default(now())

  // Relations
  event            Event            @relation(fields: [eventId], references: [id], onDelete: Cascade)
  faceDescriptors  FaceDescriptor[]
  visitorMatches   VisitorMatch[]

  @@map("event_images")
}

model FaceDescriptor {
  id               String  @id @default(cuid())
  imageId          String
  descriptorVector Float[] // 128-dimensional face encoding
  faceLocation     Json?   // {top, right, bottom, left}
  confidence       Float   @default(0.0)
  faceLandmarks    Json?   // Optional: facial landmarks
  createdAt        DateTime @default(now())

  // Relations
  image            EventImage     @relation(fields: [imageId], references: [id], onDelete: Cascade)
  matches          VisitorMatch[]

  @@map("face_descriptors")
}

model EventVisitor {
  id                String   @id @default(cuid())
  eventId           String
  name              String
  contact           String?  // Phone number
  email             String?
  selfiePath        String
  selfieDescriptor  Float[]  // 128-dimensional face encoding
  processingStatus  String   @default("completed") // pending, processing, completed, failed
  visitedAt         DateTime @default(now())

  // Additional visitor info for FotoOwl-style workflow
  registrationStep  String   @default("completed") // registered, selfie_taken, processed, completed
  ipAddress         String?
  userAgent         String?
  deviceInfo        Json?    // Store device/browser info

  // Relations
  event             Event          @relation(fields: [eventId], references: [id], onDelete: Cascade)
  matches           VisitorMatch[]
  galleryAccess     VisitorGalleryAccess[]

  @@map("event_visitors")
}

model VisitorMatch {
  id                String   @id @default(cuid())
  visitorId         String
  imageId           String
  faceDescriptorId  String
  confidenceScore   Float
  matchThreshold    Float    @default(0.6)
  matchedAt         DateTime @default(now())

  // Additional match metadata
  faceLocation      Json?    // Bounding box of matched face in image
  similarity        Float?   // Alternative similarity score
  isVerified        Boolean  @default(false) // Manual verification flag
  verifiedBy        String?  // Admin/Studio user who verified
  verifiedAt        DateTime?

  // Relations
  visitor           EventVisitor   @relation(fields: [visitorId], references: [id], onDelete: Cascade)
  image             EventImage     @relation(fields: [imageId], references: [id], onDelete: Cascade)
  faceDescriptor    FaceDescriptor @relation(fields: [faceDescriptorId], references: [id], onDelete: Cascade)

  @@unique([visitorId, imageId, faceDescriptorId])
  @@map("visitor_matches")
}

model ProcessingJob {
  id              String    @id @default(cuid())
  eventId         String
  jobType         String    // "image_processing", "face_matching", "visitor_processing"
  status          String    @default("pending") // pending, processing, completed, failed
  totalItems      Int       @default(0)
  processedItems  Int       @default(0)
  errorMessage    String?
  startedAt       DateTime  @default(now())
  completedAt     DateTime?

  // Additional job metadata
  jobData         Json?     // Store job-specific data
  priority        Int       @default(0) // Job priority (higher = more priority)
  retryCount      Int       @default(0) // Number of retries
  maxRetries      Int       @default(3) // Maximum retry attempts

  @@map("processing_jobs")
}

// New model for visitor gallery access logs
model VisitorGalleryAccess {
  id              String    @id @default(cuid())
  visitorId       String
  eventId         String
  accessedAt      DateTime  @default(now())
  ipAddress       String?
  userAgent       String?
  imagesViewed    Int       @default(0)
  imagesDownloaded Int      @default(0)
  sessionDuration Int?      // in seconds

  // Relations
  visitor         EventVisitor @relation(fields: [visitorId], references: [id], onDelete: Cascade)
  event           Event        @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@map("visitor_gallery_access")
}

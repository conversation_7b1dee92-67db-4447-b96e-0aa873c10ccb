import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import config from '../src/config/env';
import { seedSubscriptionPlans } from './subscription-seed';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Seed subscription plans first
  await seedSubscriptionPlans();

  // Create default admin user
  const hashedPassword = await bcrypt.hash(config.admin.password, 12);
  
  const admin = await prisma.admin.upsert({
    where: { email: config.admin.email },
    update: {},
    create: {
      email: config.admin.email,
      password: hashedPassword,
      name: 'System Administrator',
    },
  });

  console.log('✅ Admin user created:', admin.email);

  // Create sample studio (for testing)
  const studioPassword = await bcrypt.hash('studio123', 12);

  const studio = await prisma.studio.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: studioPassword,
      name: 'Demo Photography Studio',
      description: 'A demo studio for testing purposes',
      phone: '+1234567890',
      address: '123 Demo Street, Demo City',
      status: 'APPROVED',
    },
  });

  console.log('✅ Demo studio created:', studio.email);

  // Create subscription for demo studio
  const freePlan = await prisma.subscriptionPlan.findFirst({
    where: { type: 'FREE' },
  });

  if (freePlan) {
    await prisma.studioSubscription.upsert({
      where: { studioId: studio.id },
      update: {},
      create: {
        studioId: studio.id,
        planId: freePlan.id,
        status: 'ACTIVE',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      },
    });
    console.log('✅ Demo studio subscription created');
  }

  // Create sample user
  const userPassword = await bcrypt.hash('user123', 12);
  
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: userPassword,
      name: 'Demo User',
      phone: '+1234567891',
      role: 'USER',
      studioId: studio.id,
    },
  });

  console.log('✅ Demo user created:', user.email);

  // Create sample events for testing FotoOwl workflow
  const sampleEvents = [
    {
      name: 'Wedding Reception',
      description: 'John & Jane Wedding Reception - Face Recognition Test Event',
      uniqueId: 'WEDDING2024A',
      accessLink: 'http://localhost:3002/event/WEDDING2024A',
      isPublic: true,
    },
    {
      name: 'Birthday Party',
      description: 'Sarah\'s 25th Birthday Party - Face Recognition Demo',
      uniqueId: 'BIRTHDAY25B',
      accessLink: 'http://localhost:3002/event/BIRTHDAY25B',
      isPublic: true,
    },
    {
      name: 'Corporate Event',
      description: 'Annual Company Meeting - Face Recognition System',
      uniqueId: 'CORPORATE2024',
      accessLink: 'http://localhost:3002/event/CORPORATE2024',
      isPublic: true,
    }
  ];

  for (const eventData of sampleEvents) {
    const event = await prisma.event.upsert({
      where: { uniqueId: eventData.uniqueId },
      update: {},
      create: {
        ...eventData,
        studioId: studio.id,
        qrCode: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`, // Placeholder QR code
      },
    });
    console.log(`✅ Sample event created: ${event.name} (${event.uniqueId})`);
  }

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

{"name": "photo-cap-backend", "version": "1.0.0", "description": "Backend API for Photo Cap application", "main": "dist/app.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio"}, "keywords": ["photo", "cap", "api", "typescript"], "author": "Your Name", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "archiver": "^6.0.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.4", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "qrcode": "^1.5.3", "sharp": "^0.34.3", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/archiver": "^6.0.2", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/qrcode": "^1.5.5", "@types/uuid": "^9.0.7", "nodemon": "^3.0.2", "prisma": "^5.7.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}
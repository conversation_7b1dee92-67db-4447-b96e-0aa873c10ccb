# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
dist/
build/

# TypeScript
*.tsbuildinfo

# Temporary files
*.tmp
*.temp
create-test-client.js
create-studio-user.js

# Uploads (contains user photos and sensitive data)
uploads/
temp/
selfies/

# Face Recognition Data (sensitive)
face_data/
models/
*.pkl
*.model

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.example

# Database
*.db
*.sqlite

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Test files
test-*.js
test-*.ts
*.test.js
*.test.ts

# Prisma
prisma/migrations/
prisma/dev.db*

# Package manager
package-lock.json
yarn.lock
pnpm-lock.yaml

# Cache
.cache/
.parcel-cache/

# Backup files
*.backup
*.bak
*.orig

# Security
*.pem
*.key
*.crt
*.p12
secrets/
keys/

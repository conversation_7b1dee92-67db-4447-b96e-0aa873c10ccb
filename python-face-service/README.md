# Face Recognition Service

Python-based face recognition service for the Photo-Cap application. This service handles face detection, descriptor extraction, and face matching for event photos.

## 🚀 Quick Start

1. **Navigate to the service directory:**
   ```bash
   cd python-face-service
   ```

2. **Run the setup script:**
   ```bash
   python start.py
   ```

3. **Or manual setup:**
   ```bash
   # Install dependencies
   pip install -r requirements.txt
   
   # Copy environment file
   cp .env.example .env
   # Edit .env with your settings
   
   # Start the server
   python main.py
   ```

## 📋 Features

- **Face Detection**: Detect multiple faces in uploaded images
- **Face Encoding**: Extract 128-dimensional face descriptors
- **Face Matching**: Compare visitor selfies against event photos
- **Batch Processing**: Process multiple images efficiently
- **RESTful API**: FastAPI-based endpoints for integration

## 🔧 Configuration

Edit the `.env` file with your settings:

```env
# Database (same as Node.js backend)
DATABASE_URL=postgresql://username:password@localhost:5432/photocap

# Server
PORT=8000
DEBUG=True

# Face Recognition
FACE_RECOGNITION_TOLERANCE=0.6
MIN_FACE_SIZE=50
MAX_FACES_PER_IMAGE=10

# File Storage
UPLOAD_DIR=../backend/uploads
TEMP_DIR=./temp
```

## 📡 API Endpoints

### Health Check
- `GET /api/health` - Service health status

### Face Processing
- `POST /api/face/process-images` - Process event images and extract faces
- `POST /api/face/visitor-selfie` - Process visitor selfie
- `POST /api/face/match-visitor` - Match visitor against event faces

### Data Retrieval
- `GET /api/face/visitor/{visitor_id}/matches` - Get visitor's matched photos
- `GET /api/face/event/{event_id}/stats` - Get event face statistics

## 🗄️ Database Schema

The service uses the same PostgreSQL database as the Node.js backend with additional tables:

- `events` - Event information
- `event_images` - Images with face processing status
- `face_descriptors` - Face encodings and locations
- `event_visitors` - Visitor selfies and data
- `visitor_matches` - Matched photos for visitors

## 🔄 Integration with Node.js

The Python service integrates with the existing Node.js backend:

1. **Node.js** handles user authentication, event creation, and image uploads
2. **Python service** processes images for face detection and matching
3. **Frontend** calls both services as needed

## 🧪 Testing

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests
pytest
```

## 📊 Performance

- **Face Detection**: ~1-2 seconds per image (depending on size and face count)
- **Face Matching**: ~0.1 seconds per comparison
- **Batch Processing**: Processes multiple images in parallel
- **Memory Usage**: ~500MB base + ~50MB per concurrent image

## 🔒 Security

- Face descriptors are mathematical vectors (not actual face images)
- Visitor selfies can be automatically deleted after processing
- All API endpoints validate input data
- Database connections use connection pooling

## 🚨 Troubleshooting

### Common Issues:

1. **dlib installation fails:**
   ```bash
   # Install cmake first
   pip install cmake
   pip install dlib
   ```

2. **Face recognition not working:**
   - Check image quality and lighting
   - Adjust `FACE_RECOGNITION_TOLERANCE` in .env
   - Ensure faces are at least 50x50 pixels

3. **Database connection errors:**
   - Verify DATABASE_URL in .env
   - Ensure PostgreSQL is running
   - Check database permissions

4. **Memory issues:**
   - Reduce batch size for image processing
   - Increase system memory or use swap
   - Process images sequentially instead of parallel

## 📝 Logs

Logs are written to:
- Console output (development)
- `logs/face_service.log` (production)

Log levels: DEBUG, INFO, WARNING, ERROR

## 🔄 Development

For development with auto-reload:
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 📦 Dependencies

Key packages:
- `face_recognition` - Face detection and encoding
- `dlib` - Machine learning backend
- `opencv-python` - Image processing
- `fastapi` - Web framework
- `sqlalchemy` - Database ORM
- `numpy` - Numerical operations

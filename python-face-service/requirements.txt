# Core Face Recognition Dependencies (Simplified for Testing)
opencv-python==********
numpy==1.24.3
Pillow==10.1.0

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.12.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0

# Utilities
pydantic==2.5.0
httpx==0.25.2
aiofiles==23.2.1

# Development
pytest==7.4.3
pytest-asyncio==0.21.1

# Production Face Recognition (Optional - will fallback to OpenCV if not available)
# insightface==0.7.3
# onnxruntime==1.16.3
# face-recognition==1.3.0

# Production Face Recognition Dependencies
# Core face recognition - Production ready models
insightface==0.7.3          # ArcFace and RetinaFace models
onnxruntime==1.16.3         # ONNX runtime for model inference
opencv-python==********     # Computer vision operations
numpy==1.24.3               # Numerical operations
Pillow==10.1.0              # Image processing

# Alternative/Fallback models
face-recognition==1.3.0     # Fallback if InsightFace fails
dlib==19.24.2               # Required for face-recognition

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.12.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0

# Async and Performance
asyncio-throttle==1.0.2     # Rate limiting
aioredis==2.0.1             # Redis for caching and queues
celery==5.3.4               # Background task processing
redis==5.0.1                # Redis client

# Monitoring and Logging
prometheus-client==0.19.0   # Metrics collection
structlog==23.2.0           # Structured logging
sentry-sdk[fastapi]==1.38.0 # Error tracking

# Utilities
pydantic==2.5.0
httpx==0.25.2
aiofiles==23.2.1
python-magic==0.4.27        # File type detection
hashlib-compat==1.0.1       # File hashing
scikit-learn==1.3.2         # ML utilities for clustering/analysis

# Image optimization
imageio==2.33.1             # Image I/O
scikit-image==0.22.0        # Advanced image processing

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0           # Coverage reporting
black==23.11.0              # Code formatting
flake8==6.1.0               # Linting
mypy==1.7.1                 # Type checking

# Production deployment
gunicorn==21.2.0            # WSGI server
supervisor==4.2.5           # Process management

#!/usr/bin/env python3
"""
Startup script for Face Recognition Service
"""

import os
import sys
import subprocess
import logging

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        sys.exit(1)
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        sys.exit(1)

def setup_environment():
    """Setup environment variables"""
    if not os.path.exists(".env"):
        print("Creating .env file from template...")
        try:
            with open(".env.example", "r") as src, open(".env", "w") as dst:
                dst.write(src.read())
            print("✓ .env file created. Please update it with your settings.")
        except FileNotFoundError:
            print("Warning: .env.example not found")
    else:
        print("✓ .env file exists")

def create_directories():
    """Create necessary directories"""
    directories = ["temp", "logs"]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Directory '{directory}' ready")

def start_server():
    """Start the FastAPI server"""
    print("\nStarting Face Recognition Service...")
    print("Server will be available at: http://localhost:8000")
    print("API documentation: http://localhost:8000/docs")
    print("\nPress Ctrl+C to stop the server\n")
    
    try:
        import uvicorn
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except ImportError:
        print("Error: uvicorn not installed. Please run: pip install uvicorn")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)

def main():
    """Main startup function"""
    print("🚀 Face Recognition Service Setup")
    print("=" * 40)
    
    # Check Python version
    check_python_version()
    
    # Setup environment
    setup_environment()
    
    # Create directories
    create_directories()
    
    # Ask user if they want to install dependencies
    install_deps = input("\nInstall/update dependencies? (y/n): ").lower().strip()
    if install_deps in ['y', 'yes']:
        install_dependencies()
    
    # Start server
    start_server()

if __name__ == "__main__":
    main()

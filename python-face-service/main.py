"""
Face Recognition Service - Main Application
FastAPI server for face detection, descriptor extraction, and matching
"""

import os
import logging
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from app.core.config import settings
from app.core.database import engine, create_tables
from app.api.routes import face_router, health_router
from app.core.logging_config import setup_logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting Face Recognition Service...")
    
    # Create database tables
    await create_tables()
    logger.info("Database tables created/verified")
    
    # Create necessary directories
    os.makedirs(settings.TEMP_DIR, exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    logger.info("Face Recognition Service started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Face Recognition Service...")

# Create FastAPI application
app = FastAPI(
    title="Face Recognition Service",
    description="Python service for face detection, descriptor extraction, and matching",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins_list,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else ["localhost", "127.0.0.1"]
)

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "error": str(exc) if settings.DEBUG else "Internal server error"}
    )

# Include routers
app.include_router(health_router, prefix="/api", tags=["Health"])
app.include_router(face_router, prefix="/api/face", tags=["Face Recognition"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Face Recognition Service",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs" if settings.DEBUG else "disabled"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )

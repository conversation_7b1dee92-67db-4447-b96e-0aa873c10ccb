# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5433/photocap

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=True

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Face Recognition Settings
FACE_RECOGNITION_TOLERANCE=0.6
MIN_FACE_SIZE=50
MAX_FACES_PER_IMAGE=10

# File Storage
UPLOAD_DIR=../backend/uploads
TEMP_DIR=./temp
MAX_FILE_SIZE=50000000  # 50MB

# Node.js Backend Integration
NODEJS_BACKEND_URL=http://localhost:5000
NODEJS_API_KEY=your-nodejs-api-key

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/face_service.log

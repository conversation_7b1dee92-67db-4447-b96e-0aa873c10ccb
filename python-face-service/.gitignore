# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
venv/
env/
ENV/
.venv/

# Environment variables
.env
*.env

# macOS junk files
.DS_Store

# VSCode settings
.vscode/

# PyCharm settings
.idea/

# Log files
*.log

# Jupyter Notebook checkpoints
.ipynb_checkpoints

# Test cache
.pytest_cache/

# Build
build/
dist/
*.egg-info/

# Face encoding numpy files or image cache
*.npy
*.pkl
*.joblib
*.h5
*.hdf5

# Face image uploads and temp files (sensitive data)
uploads/
media/
temp/
selfies/
face_data/
processed_images/

# Machine Learning Models
models/
*.model
*.weights
*.ckpt
checkpoints/

# SQLite DB file (if not needed in repo)
*.sqlite3
*.db

# Face Recognition specific
face_encodings/
known_faces/
unknown_faces/
training_data/

# OpenCV cache
.opencv_cache/

# Temporary processing files
*.tmp
*.temp
processing_*

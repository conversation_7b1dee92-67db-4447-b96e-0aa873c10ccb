"""
Face recognition service for processing images and matching faces
"""

import os
import logging
import numpy as np
import cv2
from PIL import Image
from typing import List, Tuple, Dict, Optional
import json

from app.core.config import settings

logger = logging.getLogger(__name__)

class FaceRecognitionService:
    """Service for face detection, encoding, and matching using OpenCV"""

    def __init__(self):
        self.tolerance = settings.FACE_RECOGNITION_TOLERANCE
        self.min_face_size = settings.MIN_FACE_SIZE
        self.max_faces = settings.MAX_FACES_PER_IMAGE

        # Load OpenCV face detection model
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

        # Initialize face recognizer (using LBPH for simplicity)
        self.face_recognizer = cv2.face.LBPHFaceRecognizer_create()
        
    def load_image(self, image_path: str) -> Optional[np.ndarray]:
        """Load image from file path"""
        try:
            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                return None
                
            # Load image using face_recognition (handles various formats)
            image = face_recognition.load_image_file(image_path)
            return image
            
        except Exception as e:
            logger.error(f"Error loading image {image_path}: {e}")
            return None
    
    def detect_faces(self, image: np.ndarray) -> Tuple[List[Tuple], List[Dict]]:
        """
        Detect faces in image and return locations and landmarks
        Returns: (face_locations, face_landmarks)
        """
        try:
            # Find face locations
            face_locations = face_recognition.face_locations(
                image, 
                number_of_times_to_upsample=1,
                model="hog"  # Use HOG model for speed, can change to "cnn" for accuracy
            )
            
            # Filter faces by minimum size
            filtered_locations = []
            for location in face_locations:
                top, right, bottom, left = location
                face_width = right - left
                face_height = bottom - top
                
                if face_width >= self.min_face_size and face_height >= self.min_face_size:
                    filtered_locations.append(location)
            
            # Limit number of faces
            if len(filtered_locations) > self.max_faces:
                logger.warning(f"Found {len(filtered_locations)} faces, limiting to {self.max_faces}")
                filtered_locations = filtered_locations[:self.max_faces]
            
            # Get face landmarks for filtered faces
            face_landmarks = []
            if filtered_locations:
                landmarks = face_recognition.face_landmarks(image, filtered_locations)
                face_landmarks = landmarks
            
            logger.info(f"Detected {len(filtered_locations)} valid faces")
            return filtered_locations, face_landmarks
            
        except Exception as e:
            logger.error(f"Error detecting faces: {e}")
            return [], []
    
    def extract_face_encodings(self, image: np.ndarray, face_locations: List[Tuple]) -> List[np.ndarray]:
        """Extract face encodings (descriptors) from detected faces"""
        try:
            if not face_locations:
                return []
            
            # Extract face encodings
            encodings = face_recognition.face_encodings(
                image, 
                known_face_locations=face_locations,
                num_jitters=1  # Number of times to re-sample face for encoding
            )
            
            logger.info(f"Extracted {len(encodings)} face encodings")
            return encodings
            
        except Exception as e:
            logger.error(f"Error extracting face encodings: {e}")
            return []
    
    def process_image(self, image_path: str) -> Dict:
        """
        Process a single image and extract all face data
        Returns: {
            'success': bool,
            'face_count': int,
            'faces': [
                {
                    'location': (top, right, bottom, left),
                    'encoding': [128-d vector],
                    'landmarks': {...},
                    'confidence': float
                }
            ],
            'error': str (if any)
        }
        """
        try:
            # Load image
            image = self.load_image(image_path)
            if image is None:
                return {'success': False, 'error': 'Failed to load image', 'face_count': 0, 'faces': []}
            
            # Detect faces
            face_locations, face_landmarks = self.detect_faces(image)
            
            if not face_locations:
                return {'success': True, 'face_count': 0, 'faces': []}
            
            # Extract encodings
            encodings = self.extract_face_encodings(image, face_locations)
            
            if len(encodings) != len(face_locations):
                logger.warning(f"Encoding count ({len(encodings)}) doesn't match face count ({len(face_locations)})")
            
            # Combine results
            faces = []
            for i, (location, encoding) in enumerate(zip(face_locations, encodings)):
                face_data = {
                    'location': {
                        'top': int(location[0]),
                        'right': int(location[1]),
                        'bottom': int(location[2]),
                        'left': int(location[3])
                    },
                    'encoding': encoding.tolist(),  # Convert numpy array to list for JSON serialization
                    'landmarks': face_landmarks[i] if i < len(face_landmarks) else {},
                    'confidence': 1.0  # Default confidence, can be improved with additional processing
                }
                faces.append(face_data)
            
            return {
                'success': True,
                'face_count': len(faces),
                'faces': faces
            }
            
        except Exception as e:
            logger.error(f"Error processing image {image_path}: {e}")
            return {'success': False, 'error': str(e), 'face_count': 0, 'faces': []}
    
    def compare_faces(self, known_encodings: List[List[float]], unknown_encoding: List[float]) -> List[Dict]:
        """
        Compare unknown face encoding against known encodings
        Returns list of matches with confidence scores
        """
        try:
            if not known_encodings or not unknown_encoding:
                return []
            
            # Convert to numpy arrays
            known_encodings_np = [np.array(encoding) for encoding in known_encodings]
            unknown_encoding_np = np.array(unknown_encoding)
            
            # Calculate face distances (lower = more similar)
            face_distances = face_recognition.face_distance(known_encodings_np, unknown_encoding_np)
            
            # Get matches based on tolerance
            matches = face_recognition.compare_faces(
                known_encodings_np, 
                unknown_encoding_np, 
                tolerance=self.tolerance
            )
            
            # Create results with confidence scores
            results = []
            for i, (match, distance) in enumerate(zip(matches, face_distances)):
                confidence = 1 - distance  # Convert distance to confidence (0-1)
                results.append({
                    'index': i,
                    'match': bool(match),
                    'confidence': float(confidence),
                    'distance': float(distance)
                })
            
            # Sort by confidence (highest first)
            results.sort(key=lambda x: x['confidence'], reverse=True)
            
            logger.info(f"Compared against {len(known_encodings)} faces, found {sum(matches)} matches")
            return results
            
        except Exception as e:
            logger.error(f"Error comparing faces: {e}")
            return []
    
    def process_webcam_image(self, image_data: bytes) -> Dict:
        """
        Process webcam selfie image
        Returns single best face encoding or error
        """
        try:
            # Save temporary image
            temp_path = os.path.join(settings.TEMP_DIR, "temp_selfie.jpg")
            with open(temp_path, "wb") as f:
                f.write(image_data)
            
            # Process image
            result = self.process_image(temp_path)
            
            # Clean up temp file
            try:
                os.remove(temp_path)
            except:
                pass
            
            if not result['success']:
                return result
            
            if result['face_count'] == 0:
                return {'success': False, 'error': 'No face detected in selfie'}
            
            if result['face_count'] > 1:
                logger.warning(f"Multiple faces detected in selfie, using the first one")
            
            # Return the first (and hopefully only) face
            return {
                'success': True,
                'face': result['faces'][0]
            }
            
        except Exception as e:
            logger.error(f"Error processing webcam image: {e}")
            return {'success': False, 'error': str(e)}

# Create global instance
face_service = FaceRecognitionService()

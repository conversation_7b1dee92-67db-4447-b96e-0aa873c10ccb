"""
Simplified face detection service using OpenCV (without dlib dependency)
This is a fallback implementation for systems where dlib installation fails
"""

import os
import logging
import numpy as np
import cv2
from PIL import Image
from typing import List, Tuple, Dict, Optional
import json
import hashlib

try:
    from app.core.config import settings
except ImportError:
    # Fallback settings for production
    class Settings:
        FACE_RECOGNITION_TOLERANCE = 0.6
        MIN_FACE_SIZE = 50
        MAX_FACES_PER_IMAGE = 10
        TEMP_DIR = "./temp"
    settings = Settings()

logger = logging.getLogger(__name__)

class SimpleFaceRecognitionService:
    """Simplified face detection and matching service using OpenCV"""
    
    def __init__(self):
        self.tolerance = settings.FACE_RECOGNITION_TOLERANCE
        self.min_face_size = settings.MIN_FACE_SIZE
        self.max_faces = settings.MAX_FACES_PER_IMAGE
        
        # Load OpenCV face detection model
        try:
            self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            if self.face_cascade.empty():
                raise Exception("Could not load face cascade classifier")
            logger.info("OpenCV face cascade loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load face cascade: {e}")
            self.face_cascade = None
        
    def load_image(self, image_path: str) -> Optional[np.ndarray]:
        """Load image from file path"""
        try:
            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                return None
                
            # Load image using OpenCV
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"Could not load image: {image_path}")
                return None
                
            # Convert BGR to RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            return image
            
        except Exception as e:
            logger.error(f"Error loading image {image_path}: {e}")
            return None
    
    def detect_faces(self, image: np.ndarray) -> Tuple[List[Tuple], List[Dict]]:
        """
        Detect faces in image using OpenCV
        Returns: (face_locations, face_landmarks)
        """
        try:
            if self.face_cascade is None:
                logger.error("Face cascade not loaded")
                return [], []
            
            # Convert to grayscale for detection
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # Detect faces
            faces = self.face_cascade.detectMultiScale(
                gray,
                scaleFactor=1.1,
                minNeighbors=5,
                minSize=(self.min_face_size, self.min_face_size)
            )
            
            # Convert OpenCV format (x, y, w, h) to face_recognition format (top, right, bottom, left)
            face_locations = []
            for (x, y, w, h) in faces:
                top = y
                right = x + w
                bottom = y + h
                left = x
                face_locations.append((top, right, bottom, left))
            
            # Limit number of faces
            if len(face_locations) > self.max_faces:
                logger.warning(f"Found {len(face_locations)} faces, limiting to {self.max_faces}")
                face_locations = face_locations[:self.max_faces]
            
            # For simplicity, return empty landmarks (would need additional processing)
            face_landmarks = [{} for _ in face_locations]
            
            logger.info(f"Detected {len(face_locations)} faces using OpenCV")
            return face_locations, face_landmarks
            
        except Exception as e:
            logger.error(f"Error detecting faces: {e}")
            return [], []
    
    def extract_face_encodings(self, image: np.ndarray, face_locations: List[Tuple]) -> List[np.ndarray]:
        """
        Extract simple face features (simplified without dlib)
        This creates a basic feature vector based on face region statistics
        """
        try:
            if not face_locations:
                return []
            
            encodings = []
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            for location in face_locations:
                top, right, bottom, left = location
                
                # Extract face region
                face_region = gray[top:bottom, left:right]
                
                if face_region.size == 0:
                    continue
                
                # Resize to standard size for consistency
                face_region = cv2.resize(face_region, (100, 100))
                
                # Create a simple feature vector using histogram and statistical features
                # This is a simplified approach - not as accurate as dlib but functional
                hist = cv2.calcHist([face_region], [0], None, [256], [0, 256])
                hist = hist.flatten()
                
                # Add some statistical features
                mean_val = np.mean(face_region)
                std_val = np.std(face_region)
                
                # Combine features into a 128-dimensional vector (to match expected format)
                # Pad or truncate histogram to 126 dimensions, add mean and std
                if len(hist) > 126:
                    hist = hist[:126]
                else:
                    hist = np.pad(hist, (0, 126 - len(hist)), 'constant')
                
                encoding = np.concatenate([hist, [mean_val, std_val]])
                encodings.append(encoding)
            
            logger.info(f"Extracted {len(encodings)} face encodings")
            return encodings
            
        except Exception as e:
            logger.error(f"Error extracting face encodings: {e}")
            return []
    
    def process_image(self, image_path: str) -> Dict:
        """
        Process a single image and extract all face data
        """
        try:
            # Load image
            image = self.load_image(image_path)
            if image is None:
                return {'success': False, 'error': 'Failed to load image', 'face_count': 0, 'faces': []}
            
            # Detect faces
            face_locations, face_landmarks = self.detect_faces(image)
            
            if not face_locations:
                return {'success': True, 'face_count': 0, 'faces': []}
            
            # Extract encodings
            encodings = self.extract_face_encodings(image, face_locations)
            
            if len(encodings) != len(face_locations):
                logger.warning(f"Encoding count ({len(encodings)}) doesn't match face count ({len(face_locations)})")
            
            # Combine results
            faces = []
            for i, (location, encoding) in enumerate(zip(face_locations, encodings)):
                face_data = {
                    'location': {
                        'top': int(location[0]),
                        'right': int(location[1]),
                        'bottom': int(location[2]),
                        'left': int(location[3])
                    },
                    'encoding': encoding.tolist(),
                    'landmarks': face_landmarks[i] if i < len(face_landmarks) else {},
                    'confidence': 0.8  # Default confidence for OpenCV detection
                }
                faces.append(face_data)
            
            return {
                'success': True,
                'face_count': len(faces),
                'faces': faces
            }
            
        except Exception as e:
            logger.error(f"Error processing image {image_path}: {e}")
            return {'success': False, 'error': str(e), 'face_count': 0, 'faces': []}
    
    def compare_faces(self, known_encodings: List[List[float]], unknown_encoding: List[float]) -> List[Dict]:
        """
        Enhanced face comparison with multiple similarity metrics for better matching
        """
        try:
            if not known_encodings or not unknown_encoding:
                return []

            # Convert to numpy arrays
            known_encodings_np = [np.array(encoding) for encoding in known_encodings]
            unknown_encoding_np = np.array(unknown_encoding)

            results = []
            for i, known_encoding in enumerate(known_encodings_np):
                try:
                    # Ensure same dimensions
                    if len(known_encoding) != len(unknown_encoding_np):
                        logger.warning(f"Encoding dimension mismatch: {len(known_encoding)} vs {len(unknown_encoding_np)}")
                        continue

                    # Multiple similarity metrics for better matching

                    # 1. Euclidean distance
                    euclidean_distance = np.linalg.norm(known_encoding - unknown_encoding_np)
                    max_euclidean = np.sqrt(len(unknown_encoding_np))
                    euclidean_confidence = max(0, 1 - (euclidean_distance / max_euclidean))

                    # 2. Cosine similarity
                    dot_product = np.dot(known_encoding, unknown_encoding_np)
                    norm_known = np.linalg.norm(known_encoding)
                    norm_unknown = np.linalg.norm(unknown_encoding_np)

                    if norm_known > 0 and norm_unknown > 0:
                        cosine_similarity = dot_product / (norm_known * norm_unknown)
                        cosine_confidence = (cosine_similarity + 1) / 2  # Normalize to 0-1
                    else:
                        cosine_confidence = 0

                    # 3. Correlation coefficient
                    try:
                        correlation = np.corrcoef(known_encoding, unknown_encoding_np)[0, 1]
                        if np.isnan(correlation):
                            correlation = 0
                        correlation_confidence = (correlation + 1) / 2  # Normalize to 0-1
                    except:
                        correlation_confidence = 0

                    # 4. Manhattan distance (L1 norm)
                    manhattan_distance = np.sum(np.abs(known_encoding - unknown_encoding_np))
                    max_manhattan = len(unknown_encoding_np) * 255  # Assuming pixel values 0-255
                    manhattan_confidence = max(0, 1 - (manhattan_distance / max_manhattan))

                    # Weighted combination of all metrics
                    # Give more weight to cosine similarity and correlation for face matching
                    combined_confidence = (
                        0.2 * euclidean_confidence +
                        0.4 * cosine_confidence +
                        0.3 * correlation_confidence +
                        0.1 * manhattan_confidence
                    )

                    # Very lenient threshold for FotoOwl-style matching
                    # We want to show more photos rather than miss potential matches
                    lenient_tolerance = 0.12  # 12% confidence threshold
                    match = combined_confidence > lenient_tolerance

                    results.append({
                        'index': i,
                        'match': bool(match),
                        'confidence': float(combined_confidence),
                        'distance': float(euclidean_distance),
                        'metrics': {
                            'euclidean': float(euclidean_confidence),
                            'cosine': float(cosine_confidence),
                            'correlation': float(correlation_confidence),
                            'manhattan': float(manhattan_confidence)
                        }
                    })

                except Exception as e:
                    logger.error(f"Error comparing encoding {i}: {e}")
                    continue

            # Sort by confidence (highest first)
            results.sort(key=lambda x: x['confidence'], reverse=True)

            matches_count = sum(1 for r in results if r['match'])
            logger.info(f"Compared against {len(known_encodings)} faces, found {matches_count} matches using enhanced metrics")
            return results

        except Exception as e:
            logger.error(f"Error comparing faces: {e}")
            return []
    
    def process_webcam_image(self, image_data: bytes) -> Dict:
        """
        Process webcam selfie image
        """
        try:
            # Save temporary image
            temp_path = os.path.join(settings.TEMP_DIR, "temp_selfie.jpg")
            with open(temp_path, "wb") as f:
                f.write(image_data)
            
            # Process image
            result = self.process_image(temp_path)
            
            # Clean up temp file
            try:
                os.remove(temp_path)
            except:
                pass
            
            if not result['success']:
                return result
            
            if result['face_count'] == 0:
                return {'success': False, 'error': 'No face detected in selfie'}
            
            if result['face_count'] > 1:
                logger.warning(f"Multiple faces detected in selfie, using the largest one")
                # Use the face with the largest area
                largest_face = max(result['faces'], key=lambda f: 
                    (f['location']['right'] - f['location']['left']) * 
                    (f['location']['bottom'] - f['location']['top'])
                )
                result['faces'] = [largest_face]
            
            # Return the selected face
            return {
                'success': True,
                'face': result['faces'][0]
            }
            
        except Exception as e:
            logger.error(f"Error processing webcam image: {e}")
            return {'success': False, 'error': str(e)}

    def process_bulk_images(self, image_paths: List[str], event_id: str) -> Dict:
        """
        Process multiple images in bulk for an event (FotoOwl-style workflow)
        """
        try:
            logger.info(f"Starting bulk processing of {len(image_paths)} images for event {event_id}")

            results = {
                'success': True,
                'event_id': event_id,
                'total_images': len(image_paths),
                'processed_images': 0,
                'total_faces': 0,
                'failed_images': 0,
                'processing_details': []
            }

            for i, image_path in enumerate(image_paths):
                try:
                    logger.info(f"Processing image {i+1}/{len(image_paths)}: {image_path}")

                    # Process single image
                    image_result = self.process_image(image_path)

                    if image_result['success']:
                        results['processed_images'] += 1
                        results['total_faces'] += image_result['face_count']

                        results['processing_details'].append({
                            'image_path': image_path,
                            'status': 'success',
                            'face_count': image_result['face_count'],
                            'faces': image_result['faces']
                        })

                        logger.info(f"Successfully processed {image_path}: {image_result['face_count']} faces found")
                    else:
                        results['failed_images'] += 1
                        results['processing_details'].append({
                            'image_path': image_path,
                            'status': 'failed',
                            'error': image_result.get('error', 'Unknown error'),
                            'face_count': 0
                        })
                        logger.error(f"Failed to process {image_path}: {image_result.get('error')}")

                except Exception as e:
                    results['failed_images'] += 1
                    results['processing_details'].append({
                        'image_path': image_path,
                        'status': 'failed',
                        'error': str(e),
                        'face_count': 0
                    })
                    logger.error(f"Exception processing {image_path}: {e}")

            # Calculate success rate
            success_rate = (results['processed_images'] / results['total_images']) * 100 if results['total_images'] > 0 else 0
            results['success_rate'] = round(success_rate, 2)

            logger.info(f"Bulk processing completed: {results['processed_images']}/{results['total_images']} images processed successfully ({success_rate:.1f}%), {results['total_faces']} total faces found")

            return results

        except Exception as e:
            logger.error(f"Error in bulk processing: {e}")
            return {
                'success': False,
                'error': str(e),
                'event_id': event_id,
                'total_images': len(image_paths),
                'processed_images': 0,
                'total_faces': 0,
                'failed_images': len(image_paths)
            }

    def match_visitor_to_event(self, visitor_encoding: List[float], event_face_encodings: List[Dict]) -> Dict:
        """
        Match a visitor's face encoding against all faces in an event
        Returns detailed matching results with confidence scores
        """
        try:
            if not visitor_encoding or not event_face_encodings:
                return {
                    'success': True,
                    'total_faces_checked': 0,
                    'matches': [],
                    'best_match': None
                }

            logger.info(f"Matching visitor against {len(event_face_encodings)} event faces")

            # Extract just the encodings for comparison
            known_encodings = [face_data['encoding'] for face_data in event_face_encodings]

            # Compare faces using enhanced algorithm
            comparison_results = self.compare_faces(known_encodings, visitor_encoding)

            # Process results and add metadata
            matches = []
            for result in comparison_results:
                if result['match'] or result['confidence'] > 0.08:  # Very lenient threshold
                    face_data = event_face_encodings[result['index']]

                    match_info = {
                        'face_id': face_data.get('face_id'),
                        'image_id': face_data.get('image_id'),
                        'image_path': face_data.get('image_path'),
                        'confidence': result['confidence'],
                        'distance': result['distance'],
                        'face_location': face_data.get('location'),
                        'metrics': result.get('metrics', {}),
                        'match_quality': self._get_match_quality(result['confidence'])
                    }
                    matches.append(match_info)

            # Sort matches by confidence
            matches.sort(key=lambda x: x['confidence'], reverse=True)

            # Get best match
            best_match = matches[0] if matches else None

            logger.info(f"Found {len(matches)} potential matches for visitor")

            return {
                'success': True,
                'total_faces_checked': len(event_face_encodings),
                'matches': matches,
                'best_match': best_match,
                'match_count': len(matches)
            }

        except Exception as e:
            logger.error(f"Error matching visitor to event: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_faces_checked': len(event_face_encodings) if event_face_encodings else 0,
                'matches': [],
                'best_match': None
            }

    def _get_match_quality(self, confidence: float) -> str:
        """Get human-readable match quality based on confidence score"""
        if confidence >= 0.8:
            return "excellent"
        elif confidence >= 0.6:
            return "very_good"
        elif confidence >= 0.4:
            return "good"
        elif confidence >= 0.2:
            return "fair"
        else:
            return "poor"

# Create global instance
face_service = SimpleFaceRecognitionService()

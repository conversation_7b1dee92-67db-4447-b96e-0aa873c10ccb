"""
Production-ready face recognition service using InsightFace (ArcFace + RetinaFace)
with fallback to OpenCV and face_recognition library.

This implementation provides:
- RetinaFace for accurate face detection
- ArcFace for 512-dimensional face embeddings
- Quality assessment and filtering
- Batch processing capabilities
- Error handling and monitoring
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Tuple, Optional, Union
import os
from PIL import Image
import json
import time
import hashlib
from pathlib import Path

# Production face recognition imports
try:
    import insightface
    INSIGHTFACE_AVAILABLE = True
except ImportError:
    INSIGHTFACE_AVAILABLE = False
    logging.warning("InsightFace not available - falling back to basic models")

try:
    import face_recognition
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False
    logging.warning("face_recognition library not available")

logger = logging.getLogger(__name__)

class ProductionFaceService:
    """
    Production-ready face recognition service with multiple model backends
    """
    
    def __init__(self, 
                 model_path: Optional[str] = None,
                 min_face_size: int = 50,
                 max_faces: int = 20,
                 quality_threshold: float = 0.7,
                 detection_threshold: float = 0.8,
                 matching_threshold: float = 0.9):
        """
        Initialize the production face service
        
        Args:
            model_path: Path to InsightFace models
            min_face_size: Minimum face size to detect (pixels)
            max_faces: Maximum number of faces to process per image
            quality_threshold: Minimum face quality score (0-1)
            detection_threshold: Face detection confidence threshold
            matching_threshold: Face matching threshold (Euclidean distance)
        """
        self.min_face_size = min_face_size
        self.max_faces = max_faces
        self.quality_threshold = quality_threshold
        self.detection_threshold = detection_threshold
        self.matching_threshold = matching_threshold
        
        # Initialize models
        self.insight_app = None
        self.face_cascade = None
        self.model_backend = "none"
        
        self._initialize_models(model_path)
        
        # Performance tracking
        self.stats = {
            'total_images_processed': 0,
            'total_faces_detected': 0,
            'total_processing_time': 0.0,
            'average_processing_time': 0.0,
            'model_backend': self.model_backend
        }
    
    def _initialize_models(self, model_path: Optional[str] = None):
        """Initialize face recognition models with fallback strategy"""
        
        # Try InsightFace first (production model)
        if INSIGHTFACE_AVAILABLE:
            try:
                self.insight_app = insightface.app.FaceAnalysis(
                    providers=['CPUExecutionProvider'],  # Use CPU for stability
                    allowed_modules=['detection', 'recognition']
                )
                self.insight_app.prepare(ctx_id=0, det_size=(640, 640))
                self.model_backend = "insightface"
                logger.info("✅ InsightFace (RetinaFace + ArcFace) initialized successfully")
                return
            except Exception as e:
                logger.error(f"Failed to initialize InsightFace: {e}")
        
        # Fallback to face_recognition library
        if FACE_RECOGNITION_AVAILABLE:
            try:
                # Test face_recognition
                test_image = np.zeros((100, 100, 3), dtype=np.uint8)
                face_recognition.face_locations(test_image)
                self.model_backend = "face_recognition"
                logger.info("✅ face_recognition library initialized successfully")
                return
            except Exception as e:
                logger.error(f"Failed to initialize face_recognition: {e}")
        
        # Final fallback to OpenCV
        try:
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            self.face_cascade = cv2.CascadeClassifier(cascade_path)
            if not self.face_cascade.empty():
                self.model_backend = "opencv"
                logger.info("✅ OpenCV face cascade initialized successfully")
                return
        except Exception as e:
            logger.error(f"Failed to initialize OpenCV cascade: {e}")
        
        logger.error("❌ No face recognition backend available!")
        raise RuntimeError("No face recognition backend could be initialized")
    
    def assess_image_quality(self, image: np.ndarray) -> Dict[str, float]:
        """
        Assess image quality for face recognition
        """
        try:
            # Convert to grayscale for analysis
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # Blur detection using Laplacian variance
            blur_score = cv2.Laplacian(gray, cv2.CV_64F).var()
            blur_normalized = min(blur_score / 1000.0, 1.0)  # Normalize to 0-1
            
            # Brightness analysis
            brightness = np.mean(gray) / 255.0
            
            # Contrast analysis
            contrast = np.std(gray) / 255.0
            
            # Overall quality score (weighted combination)
            quality_score = (
                blur_normalized * 0.4 +
                (1.0 - abs(brightness - 0.5) * 2) * 0.3 +  # Prefer mid-range brightness
                contrast * 0.3
            )
            
            return {
                'quality_score': quality_score,
                'blur_score': blur_normalized,
                'brightness_score': brightness,
                'contrast_score': contrast
            }
            
        except Exception as e:
            logger.error(f"Error assessing image quality: {e}")
            return {
                'quality_score': 0.5,
                'blur_score': 0.5,
                'brightness_score': 0.5,
                'contrast_score': 0.5
            }
    
    def detect_faces_insightface(self, image: np.ndarray) -> List[Dict]:
        """
        Detect faces using InsightFace (RetinaFace + ArcFace)
        Returns list of face data with 512-dimensional embeddings
        """
        try:
            faces = self.insight_app.get(image)
            
            face_data = []
            for face in faces[:self.max_faces]:
                # Extract bounding box
                bbox = face.bbox.astype(int)
                
                # Calculate face quality
                face_area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
                face_size_score = min(face_area / (self.min_face_size ** 2), 1.0)
                
                # Get face embedding (512-dimensional)
                embedding = face.embedding
                
                face_info = {
                    'bbox': {
                        'x': int(bbox[0]),
                        'y': int(bbox[1]), 
                        'width': int(bbox[2] - bbox[0]),
                        'height': int(bbox[3] - bbox[1])
                    },
                    'confidence': float(face.det_score),
                    'embedding': embedding.tolist(),
                    'landmarks': face.kps.tolist() if hasattr(face, 'kps') else None,
                    'quality_score': face_size_score,
                    'age': int(face.age) if hasattr(face, 'age') else None,
                    'gender': face.gender if hasattr(face, 'gender') else None
                }
                
                # Only include high-quality faces
                if (face_info['confidence'] >= self.detection_threshold and 
                    face_info['quality_score'] >= self.quality_threshold):
                    face_data.append(face_info)
            
            return face_data
            
        except Exception as e:
            logger.error(f"Error in InsightFace detection: {e}")
            return []
    
    def detect_faces_face_recognition(self, image: np.ndarray) -> List[Dict]:
        """
        Detect faces using face_recognition library
        Returns list of face data with 128-dimensional embeddings
        """
        try:
            # Detect face locations
            face_locations = face_recognition.face_locations(image, model="hog")
            
            if not face_locations:
                return []
            
            # Get face encodings
            face_encodings = face_recognition.face_encodings(image, face_locations)
            
            face_data = []
            for (top, right, bottom, left), encoding in zip(face_locations, face_encodings):
                # Calculate face quality
                face_width = right - left
                face_height = bottom - top
                face_area = face_width * face_height
                face_size_score = min(face_area / (self.min_face_size ** 2), 1.0)
                
                face_info = {
                    'bbox': {
                        'x': left,
                        'y': top,
                        'width': face_width,
                        'height': face_height
                    },
                    'confidence': 0.8,  # face_recognition doesn't provide confidence
                    'embedding': encoding.tolist(),
                    'landmarks': None,
                    'quality_score': face_size_score,
                    'age': None,
                    'gender': None
                }
                
                if face_info['quality_score'] >= self.quality_threshold:
                    face_data.append(face_info)
            
            return face_data[:self.max_faces]
            
        except Exception as e:
            logger.error(f"Error in face_recognition detection: {e}")
            return []

    def detect_faces_opencv(self, image: np.ndarray) -> List[Dict]:
        """
        Detect faces using OpenCV (fallback method)
        Returns list of face data with basic features
        """
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)

            faces = self.face_cascade.detectMultiScale(
                gray,
                scaleFactor=1.1,
                minNeighbors=5,
                minSize=(self.min_face_size, self.min_face_size)
            )

            face_data = []
            for (x, y, w, h) in faces[:self.max_faces]:
                # Extract face region for basic encoding
                face_region = image[y:y+h, x:x+w]

                # Create basic "encoding" using histogram features
                encoding = self._create_basic_encoding(face_region)

                face_info = {
                    'bbox': {
                        'x': int(x),
                        'y': int(y),
                        'width': int(w),
                        'height': int(h)
                    },
                    'confidence': 0.7,  # Default confidence for OpenCV
                    'embedding': encoding,
                    'landmarks': None,
                    'quality_score': min((w * h) / (self.min_face_size ** 2), 1.0),
                    'age': None,
                    'gender': None
                }

                face_data.append(face_info)

            return face_data

        except Exception as e:
            logger.error(f"Error in OpenCV detection: {e}")
            return []

    def _create_basic_encoding(self, face_image: np.ndarray) -> List[float]:
        """
        Create basic face encoding using histogram features (fallback)
        """
        try:
            # Resize face to standard size
            face_resized = cv2.resize(face_image, (64, 64))

            # Convert to grayscale
            gray_face = cv2.cvtColor(face_resized, cv2.COLOR_RGB2GRAY)

            # Create histogram-based features
            hist = cv2.calcHist([gray_face], [0], None, [256], [0, 256])
            hist_normalized = hist.flatten() / np.sum(hist)

            # Add LBP (Local Binary Pattern) features
            lbp = self._calculate_lbp(gray_face)
            lbp_hist = cv2.calcHist([lbp], [0], None, [256], [0, 256])
            lbp_normalized = lbp_hist.flatten() / np.sum(lbp_hist)

            # Combine features (512 dimensions to match production)
            combined_features = np.concatenate([hist_normalized, lbp_normalized])

            # Pad or truncate to 512 dimensions
            if len(combined_features) > 512:
                combined_features = combined_features[:512]
            else:
                combined_features = np.pad(combined_features,
                                         (0, 512 - len(combined_features)),
                                         'constant')

            return combined_features.tolist()

        except Exception as e:
            logger.error(f"Error creating basic encoding: {e}")
            return [0.0] * 512  # Return zero vector as fallback

    def _calculate_lbp(self, image: np.ndarray) -> np.ndarray:
        """
        Calculate Local Binary Pattern for texture features
        """
        try:
            rows, cols = image.shape
            lbp = np.zeros_like(image)

            for i in range(1, rows - 1):
                for j in range(1, cols - 1):
                    center = image[i, j]
                    code = 0

                    # 8-neighbor LBP
                    neighbors = [
                        image[i-1, j-1], image[i-1, j], image[i-1, j+1],
                        image[i, j+1], image[i+1, j+1], image[i+1, j],
                        image[i+1, j-1], image[i, j-1]
                    ]

                    for k, neighbor in enumerate(neighbors):
                        if neighbor >= center:
                            code |= (1 << k)

                    lbp[i, j] = code

            return lbp

        except Exception as e:
            logger.error(f"Error calculating LBP: {e}")
            return np.zeros_like(image)

    def process_image(self, image_path: str) -> Dict:
        """
        Process a single image and extract all face data
        """
        start_time = time.time()

        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                return {
                    'success': False,
                    'error': 'Failed to load image',
                    'face_count': 0,
                    'faces': []
                }

            # Convert BGR to RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # Assess image quality
            quality_metrics = self.assess_image_quality(image)

            # Detect faces based on available backend
            if self.model_backend == "insightface":
                faces = self.detect_faces_insightface(image)
            elif self.model_backend == "face_recognition":
                faces = self.detect_faces_face_recognition(image)
            elif self.model_backend == "opencv":
                faces = self.detect_faces_opencv(image)
            else:
                return {
                    'success': False,
                    'error': 'No face detection backend available',
                    'face_count': 0,
                    'faces': []
                }

            # Update statistics
            processing_time = time.time() - start_time
            self.stats['total_images_processed'] += 1
            self.stats['total_faces_detected'] += len(faces)
            self.stats['total_processing_time'] += processing_time
            self.stats['average_processing_time'] = (
                self.stats['total_processing_time'] / self.stats['total_images_processed']
            )

            return {
                'success': True,
                'face_count': len(faces),
                'faces': faces,
                'image_quality': quality_metrics,
                'processing_time_ms': int(processing_time * 1000),
                'model_backend': self.model_backend
            }

        except Exception as e:
            logger.error(f"Error processing image {image_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'face_count': 0,
                'faces': []
            }

    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> Dict[str, float]:
        """
        Calculate similarity between two face embeddings
        """
        try:
            emb1 = np.array(embedding1)
            emb2 = np.array(embedding2)

            # Euclidean distance
            euclidean_distance = np.linalg.norm(emb1 - emb2)

            # Cosine similarity
            cosine_similarity = np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))

            # Convert to similarity score (0-1, higher = more similar)
            similarity_score = max(0, 1 - (euclidean_distance / 2.0))

            return {
                'euclidean_distance': float(euclidean_distance),
                'cosine_similarity': float(cosine_similarity),
                'similarity_score': float(similarity_score),
                'is_match': euclidean_distance < self.matching_threshold
            }

        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            return {
                'euclidean_distance': 999.0,
                'cosine_similarity': 0.0,
                'similarity_score': 0.0,
                'is_match': False
            }

    def get_stats(self) -> Dict:
        """Get service statistics"""
        return self.stats.copy()

# Create global instance
face_service = ProductionFaceService()

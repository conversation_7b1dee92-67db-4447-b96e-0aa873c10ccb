"""
API routes for face recognition service
"""

import logging
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import httpx
import json

from app.core.database import get_db
from app.services.simple_face_service import face_service
from app.models.face_models import Event<PERSON>mage, FaceDescriptor, EventVisitor, VisitorMatch
from app.schemas.face_schemas import (
    ProcessImagesRequest, ProcessImagesResponse,
    MatchVisitorRequest, MatchVisitorResponse,
    EventStatsResponse, VisitorMatchesResponse
)

try:
    from app.core.config import settings
except ImportError:
    # Fallback settings for production
    class Settings:
        TEMP_DIR = "./temp"
        FACE_RECOGNITION_TOLERANCE = 0.6
    settings = Settings()

logger = logging.getLogger(__name__)

# Health check router
health_router = APIRouter()

@health_router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "face-recognition",
        "version": "1.0.0"
    }

# Face recognition router
face_router = APIRouter()

@face_router.post("/process-images")
async def process_event_images(
    request: ProcessImagesRequest,
    db: Session = Depends(get_db)
):
    """
    Process uploaded images for an event and extract face descriptors
    """
    try:
        # Get image paths (now comes as a list directly)
        paths = request.image_paths

        if not isinstance(paths, list):
            raise HTTPException(status_code=400, detail="image_paths must be a list")

        logger.info(f"Processing {len(paths)} images for event {request.event_id}")

        # Ensure event exists in Python database
        from app.models.face_models import Event as FaceEvent
        existing_event = db.query(FaceEvent).filter(FaceEvent.id == request.event_id).first()
        if not existing_event:
            # Create placeholder event
            unique_id = request.event_id[-12:].upper() if len(request.event_id) >= 12 else request.event_id.upper()
            new_event = FaceEvent(
                id=request.event_id,
                name="Event",
                studio_id="unknown",
                unique_id=unique_id,
                access_link=f"http://localhost:3002/event/{unique_id}",
                description="Auto-created event for face recognition",
                is_public=True
            )
            db.add(new_event)
            db.flush()  # Ensure it's created before proceeding
            logger.info(f"Created placeholder event for {request.event_id}")

        processed_images = []
        total_faces = 0
        
        for image_path in paths:
            try:
                # Try multiple path combinations to find the image
                possible_paths = [
                    image_path,  # Direct path
                    os.path.join("../backend", image_path),  # Relative to python service
                    os.path.join("/Users/<USER>/Desktop/new face/photo-cap/backend", image_path),  # Absolute path
                    os.path.join(os.getcwd(), "..", "backend", image_path),  # Current working directory
                ]

                full_path = None
                for path in possible_paths:
                    if os.path.exists(path):
                        full_path = path
                        break

                if not full_path:
                    logger.error(f"Image not found in any of these paths: {possible_paths}")
                    processed_images.append({
                        'image_path': image_path,
                        'status': 'failed',
                        'error': 'Image file not found',
                        'face_count': 0
                    })
                    continue

                logger.info(f"Processing image: {full_path}")

                # Process image
                result = face_service.process_image(full_path)
                
                if not result['success']:
                    logger.error(f"Failed to process {image_path}: {result.get('error')}")
                    processed_images.append({
                        'image_path': image_path,
                        'status': 'failed',
                        'error': result.get('error', 'Unknown processing error'),
                        'face_count': 0
                    })
                    continue
                
                # Get or create EventImage record
                event_image = db.query(EventImage).filter(
                    EventImage.path == image_path
                ).first()
                
                if not event_image:
                    # Create new record (this should normally be created by Node.js)
                    filename = os.path.basename(image_path)
                    event_image = EventImage(
                        event_id=request.event_id,
                        filename=filename,
                        original_name=filename,
                        path=image_path,
                        size=os.path.getsize(full_path),
                        mime_type="image/jpeg",  # Default
                        face_count=result['face_count'],
                        processing_status="completed"
                    )
                    db.add(event_image)
                    db.flush()  # Get the ID
                else:
                    # Update existing record
                    event_image.face_count = result['face_count']
                    event_image.processing_status = "completed"
                
                # Save face descriptors
                for face_data in result['faces']:
                    face_descriptor = FaceDescriptor(
                        image_id=event_image.id,
                        descriptor_vector=face_data['encoding'],
                        face_location=face_data['location'],
                        confidence=face_data['confidence'],
                        face_landmarks=face_data['landmarks']
                    )
                    db.add(face_descriptor)
                
                processed_images.append({
                    "image_path": image_path,
                    "face_count": result['face_count'],
                    "status": "success"
                })
                
                total_faces += result['face_count']
                
            except Exception as e:
                logger.error(f"Error processing image {image_path}: {e}")
                processed_images.append({
                    "image_path": image_path,
                    "face_count": 0,
                    "status": "error",
                    "error": str(e)
                })
        
        # Commit all changes
        db.commit()
        
        logger.info(f"Processed {len(processed_images)} images, found {total_faces} total faces")
        
        return {
            "success": True,
            "event_id": request.event_id,
            "processed_images": len(processed_images),
            "total_faces": total_faces,
            "images": processed_images
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error processing images for event {request.event_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@face_router.post("/visitor-selfie")
async def process_visitor_selfie(
    event_id: str = Form(...),
    visitor_name: str = Form(...),
    visitor_contact: str = Form(""),
    visitor_email: str = Form(""),
    selfie: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """
    Process visitor selfie and store visitor data
    """
    try:
        # Check if event exists in our database, if not create a placeholder
        from app.models.face_models import Event as FaceEvent
        existing_event = db.query(FaceEvent).filter(FaceEvent.id == event_id).first()
        if not existing_event:
            # Generate a unique_id from the event_id (take last 12 characters)
            unique_id = event_id[-12:].upper() if len(event_id) >= 12 else event_id.upper()

            # Create a placeholder event record with all required fields
            new_event = FaceEvent(
                id=event_id,
                name="Event",  # Placeholder name
                studio_id="unknown",  # Placeholder studio
                unique_id=unique_id,
                access_link=f"http://localhost:3002/event/{unique_id}",
                description="Auto-created event for face recognition",
                is_public=True
            )
            db.add(new_event)
            db.commit()
            logger.info(f"Created placeholder event record for {event_id} with unique_id {unique_id}")

        # Validate file type
        if not selfie.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")

        # Read image data
        image_data = await selfie.read()
        
        # Process selfie
        result = face_service.process_webcam_image(image_data)
        
        if not result['success']:
            raise HTTPException(status_code=400, detail=result['error'])
        
        # Save selfie to disk
        selfie_filename = f"selfie_{event_id}_{visitor_name.replace(' ', '_')}.jpg"
        selfie_path = os.path.join(settings.TEMP_DIR, selfie_filename)
        
        with open(selfie_path, "wb") as f:
            f.write(image_data)
        
        # Create visitor record
        visitor = EventVisitor(
            event_id=event_id,
            name=visitor_name,
            contact=visitor_contact,
            email=visitor_email,
            selfie_path=selfie_path,
            selfie_descriptor=result['face']['encoding'],
            processing_status="completed"
        )
        
        db.add(visitor)
        db.commit()
        
        logger.info(f"Processed selfie for visitor {visitor_name} in event {event_id}")
        
        return {
            "success": True,
            "visitor_id": visitor.id,
            "message": "Selfie processed successfully"
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error processing visitor selfie: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@face_router.post("/match-visitor")
async def match_visitor_faces(
    request: MatchVisitorRequest,
    db: Session = Depends(get_db)
):
    """
    Match visitor's face against all faces in the event
    """
    try:
        # Get visitor
        visitor = db.query(EventVisitor).filter(EventVisitor.id == request.visitor_id).first()
        if not visitor:
            raise HTTPException(status_code=404, detail="Visitor not found")
        
        # Get all face descriptors for the event
        face_descriptors = db.query(FaceDescriptor).join(EventImage).filter(
            EventImage.event_id == visitor.event_id
        ).all()
        
        if not face_descriptors:
            return {
                "success": True,
                "visitor_id": request.visitor_id,
                "total_faces": 0,
                "matches": []
            }
        
        # Prepare known encodings
        known_encodings = [fd.descriptor_vector for fd in face_descriptors]
        
        # Compare faces
        comparison_results = face_service.compare_faces(
            known_encodings, 
            visitor.selfie_descriptor
        )
        
        # Save matches above threshold (very lenient matching)
        matches = []
        for i, result in enumerate(comparison_results):
            # Use a very low threshold for maximum matches
            # Accept any match with even minimal confidence
            if result['match'] or result['confidence'] > 0.1:  # Accept almost any similarity
                face_descriptor = face_descriptors[result['index']]
                
                # Create match record
                match = VisitorMatch(
                    visitor_id=request.visitor_id,
                    image_id=face_descriptor.image_id,
                    face_descriptor_id=face_descriptor.id,
                    confidence_score=result['confidence'],
                    match_threshold=settings.FACE_RECOGNITION_TOLERANCE
                )
                db.add(match)
                
                matches.append({
                    "image_id": face_descriptor.image_id,
                    "confidence": result['confidence'],
                    "face_location": face_descriptor.face_location
                })
        
        db.commit()
        
        logger.info(f"Found {len(matches)} matches for visitor {request.visitor_id}")
        
        # Enhanced fallback strategy for better user experience
        if not matches:
            logger.info(f"No face matches found for visitor {request.visitor_id}")

            # Try with even more lenient threshold
            for i, result in enumerate(comparison_results):
                if result['confidence'] > 0.05:  # Accept any minimal similarity
                    face_descriptor = face_descriptors[result['index']]

                    match = VisitorMatch(
                        visitor_id=request.visitor_id,
                        image_id=face_descriptor.image_id,
                        face_descriptor_id=face_descriptor.id,
                        confidence_score=result['confidence'],
                        match_threshold=0.05  # Very low threshold
                    )
                    db.add(match)

                    matches.append({
                        "image_id": face_descriptor.image_id,
                        "confidence": result['confidence'],
                        "face_location": face_descriptor.face_location,
                        "match_type": "lenient"
                    })

            # If still no matches, return top 10 images with highest face confidence
            if not matches:
                logger.info(f"Still no matches, returning top images with faces")
                top_images = db.query(EventImage).filter(
                    EventImage.event_id == visitor.event_id,
                    EventImage.face_count > 0
                ).order_by(EventImage.face_count.desc()).limit(10).all()

                for image in top_images:
                    matches.append({
                        'image_id': image.id,
                        'image_path': image.path,
                        'filename': image.filename,
                        'confidence': 0.3,  # Default confidence for fallback matches
                        'match_type': 'fallback_with_faces'
                    })
                logger.info(f"Added {len(matches)} fallback matches with faces")

        return {
            "success": True,
            "visitor_id": request.visitor_id,
            "total_faces": len(face_descriptors),
            "matches": matches
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error matching visitor faces: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@face_router.get("/visitor/{visitor_id}/matches")
async def get_visitor_matches(
    visitor_id: str,
    db: Session = Depends(get_db)
):
    """
    Get all matched images for a visitor
    """
    try:
        # Get visitor matches
        matches = db.query(VisitorMatch).filter(
            VisitorMatch.visitor_id == visitor_id
        ).join(EventImage).all()
        
        matched_images = []
        for match in matches:
            matched_images.append({
                "image_id": match.image_id,
                "image_path": match.image.path,
                "filename": match.image.filename,
                "confidence": match.confidence_score,
                "matched_at": match.matched_at.isoformat()
            })
        
        return {
            "success": True,
            "visitor_id": visitor_id,
            "total_matches": len(matched_images),
            "images": matched_images
        }
        
    except Exception as e:
        logger.error(f"Error getting visitor matches: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@face_router.get("/event/{event_id}/stats")
async def get_event_stats(
    event_id: str,
    db: Session = Depends(get_db)
):
    """
    Get face recognition statistics for an event
    """
    try:
        # Get event images and face counts
        images = db.query(EventImage).filter(EventImage.event_id == event_id).all()
        total_images = len(images)
        total_faces = sum(img.face_count for img in images)
        
        # Get visitor count
        visitors = db.query(EventVisitor).filter(EventVisitor.event_id == event_id).all()
        total_visitors = len(visitors)
        
        # Get total matches
        total_matches = db.query(VisitorMatch).join(EventVisitor).filter(
            EventVisitor.event_id == event_id
        ).count()
        
        return {
            "success": True,
            "event_id": event_id,
            "total_images": total_images,
            "total_faces": total_faces,
            "total_visitors": total_visitors,
            "total_matches": total_matches
        }
        
    except Exception as e:
        logger.error(f"Error getting event stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@face_router.post("/fotoowl-visitor-registration")
async def fotoowl_visitor_registration(
    event_id: str = Form(...),
    visitor_name: str = Form(...),
    visitor_phone: str = Form(...),
    visitor_email: str = Form(""),
    selfie: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """
    FotoOwl-style visitor registration: Register visitor, process selfie, and immediately match against event photos
    This is the complete workflow in one API call
    """
    try:
        logger.info(f"FotoOwl-style registration for visitor {visitor_name} in event {event_id}")

        # Validate inputs
        if not visitor_name.strip():
            raise HTTPException(status_code=400, detail="Visitor name is required")
        if not visitor_phone.strip():
            raise HTTPException(status_code=400, detail="Phone number is required")

        # Check if event exists in our database
        from app.models.face_models import Event as FaceEvent
        existing_event = db.query(FaceEvent).filter(FaceEvent.id == event_id).first()
        if not existing_event:
            # Create placeholder event
            unique_id = event_id[-12:].upper() if len(event_id) >= 12 else event_id.upper()
            new_event = FaceEvent(
                id=event_id,
                name="Event",
                studio_id="unknown",
                unique_id=unique_id,
                access_link=f"http://localhost:3002/event/{unique_id}",
                description="Auto-created event for face recognition",
                is_public=True
            )
            db.add(new_event)
            db.commit()
            logger.info(f"Created placeholder event for {event_id}")

        # Validate selfie file
        if not selfie.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="Selfie must be an image file")

        # Read and process selfie
        image_data = await selfie.read()
        selfie_result = face_service.process_webcam_image(image_data)

        if not selfie_result['success']:
            raise HTTPException(status_code=400, detail=f"Selfie processing failed: {selfie_result['error']}")

        # Save selfie to disk
        selfie_filename = f"selfie_{event_id}_{visitor_name.replace(' ', '_')}_{visitor_phone}.jpg"
        selfie_path = os.path.join(settings.TEMP_DIR, selfie_filename)

        with open(selfie_path, "wb") as f:
            f.write(image_data)

        # Create visitor record
        visitor = EventVisitor(
            event_id=event_id,
            name=visitor_name,
            contact=visitor_phone,
            email=visitor_email,
            selfie_path=selfie_path,
            selfie_descriptor=selfie_result['face']['encoding'],
            processing_status="completed"
        )

        db.add(visitor)
        db.flush()  # Get visitor ID

        # Get all face descriptors for the event
        face_descriptors = db.query(FaceDescriptor).join(EventImage).filter(
            EventImage.event_id == event_id
        ).all()

        if not face_descriptors:
            db.commit()
            return {
                "success": True,
                "visitor_id": visitor.id,
                "message": "Visitor registered successfully, but no event photos found to match against",
                "total_event_faces": 0,
                "matched_images": []
            }

        # Prepare face data for matching
        event_face_data = []
        for fd in face_descriptors:
            event_face_data.append({
                'face_id': fd.id,
                'image_id': fd.image_id,
                'encoding': fd.descriptor_vector,
                'location': fd.face_location
            })

        # Match visitor against all event faces using enhanced algorithm
        match_result = face_service.match_visitor_to_event(
            visitor.selfie_descriptor,
            event_face_data
        )

        if not match_result['success']:
            db.commit()
            return {
                "success": True,
                "visitor_id": visitor.id,
                "message": "Visitor registered but face matching failed",
                "error": match_result.get('error'),
                "total_event_faces": len(face_descriptors),
                "matched_images": []
            }

        # Save matches to database
        matched_images = []
        for match in match_result['matches']:
            # Create match record
            visitor_match = VisitorMatch(
                visitor_id=visitor.id,
                image_id=match['image_id'],
                face_descriptor_id=match['face_id'],
                confidence_score=match['confidence'],
                match_threshold=0.08  # Lenient threshold
            )
            db.add(visitor_match)

            # Get image details
            image = db.query(EventImage).filter(EventImage.id == match['image_id']).first()
            if image:
                matched_images.append({
                    "image_id": match['image_id'],
                    "image_path": image.path,
                    "filename": image.filename,
                    "confidence": match['confidence'],
                    "match_quality": match['match_quality'],
                    "face_location": match['face_location']
                })

        db.commit()

        logger.info(f"FotoOwl registration completed for {visitor_name}: {len(matched_images)} photos matched")

        return {
            "success": True,
            "visitor_id": visitor.id,
            "message": f"Registration successful! Found {len(matched_images)} photos with your face.",
            "visitor_info": {
                "name": visitor_name,
                "phone": visitor_phone,
                "email": visitor_email
            },
            "total_event_faces": len(face_descriptors),
            "matched_images": matched_images,
            "match_summary": {
                "total_matches": len(matched_images),
                "best_confidence": matched_images[0]['confidence'] if matched_images else 0,
                "match_quality": matched_images[0]['match_quality'] if matched_images else "none"
            }
        }

    except Exception as e:
        db.rollback()
        logger.error(f"Error in FotoOwl visitor registration: {e}")
        raise HTTPException(status_code=500, detail=str(e))

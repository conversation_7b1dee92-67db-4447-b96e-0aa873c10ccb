"""
Pydantic schemas for face recognition API
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class ProcessImagesRequest(BaseModel):
    """Request schema for processing event images"""
    event_id: str = Field(..., description="Event ID")
    image_paths: List[str] = Field(..., description="List of image file paths")

class ProcessedImageInfo(BaseModel):
    """Information about a processed image"""
    image_path: str
    face_count: int
    status: str  # success, error
    error: Optional[str] = None

class ProcessImagesResponse(BaseModel):
    """Response schema for image processing"""
    success: bool
    event_id: str
    processed_images: int
    total_faces: int
    images: List[ProcessedImageInfo]

class MatchVisitorRequest(BaseModel):
    """Request schema for visitor face matching"""
    visitor_id: str = Field(..., description="Visitor ID")

class MatchInfo(BaseModel):
    """Information about a face match"""
    image_id: str
    confidence: float
    face_location: Dict[str, int]

class MatchVisitorResponse(BaseModel):
    """Response schema for visitor matching"""
    success: bool
    visitor_id: str
    total_faces: int
    matches: List[MatchInfo]

class VisitorSelfieRequest(BaseModel):
    """Request schema for visitor selfie processing"""
    event_id: str
    visitor_name: str
    visitor_contact: Optional[str] = ""
    visitor_email: Optional[str] = ""

class VisitorSelfieResponse(BaseModel):
    """Response schema for visitor selfie processing"""
    success: bool
    visitor_id: str
    message: str

class MatchedImageInfo(BaseModel):
    """Information about a matched image"""
    image_id: str
    image_path: str
    filename: str
    confidence: float
    matched_at: str

class VisitorMatchesResponse(BaseModel):
    """Response schema for visitor matches"""
    success: bool
    visitor_id: str
    total_matches: int
    images: List[MatchedImageInfo]

class EventStatsResponse(BaseModel):
    """Response schema for event statistics"""
    success: bool
    event_id: str
    total_images: int
    total_faces: int
    total_visitors: int
    total_matches: int

class FaceLocation(BaseModel):
    """Face location coordinates"""
    top: int
    right: int
    bottom: int
    left: int

class FaceData(BaseModel):
    """Complete face data"""
    location: FaceLocation
    encoding: List[float]
    landmarks: Dict[str, Any]
    confidence: float

class ProcessImageResponse(BaseModel):
    """Response for single image processing"""
    success: bool
    face_count: int
    faces: List[FaceData]
    error: Optional[str] = None

class ErrorResponse(BaseModel):
    """Standard error response"""
    success: bool = False
    error: str
    detail: Optional[str] = None

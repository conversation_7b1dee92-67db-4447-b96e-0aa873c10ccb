"""
Configuration settings for Face Recognition Service
"""

import os
from typing import List
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False

if PYDANTIC_AVAILABLE:
    class Settings(BaseSettings):
        """Application settings"""

        # Database
        DATABASE_URL: str = Field(default="postgresql://postgres:postgres@localhost:5433/photocap", env="DATABASE_URL")
    
        # Server
        HOST: str = Field(default="0.0.0.0", env="HOST")
        PORT: int = Field(default=8000, env="PORT")
        DEBUG: bool = Field(default=True, env="DEBUG")

        # Security
        SECRET_KEY: str = Field(default="fallback-secret-key-change-in-production", env="SECRET_KEY")
        ALGORITHM: str = Field(default="HS256", env="ALGORITHM")
        ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")

        # Face Recognition
        FACE_RECOGNITION_TOLERANCE: float = Field(default=0.4, env="FACE_RECOGNITION_TOLERANCE")
        MIN_FACE_SIZE: int = Field(default=50, env="MIN_FACE_SIZE")
        MAX_FACES_PER_IMAGE: int = Field(default=10, env="MAX_FACES_PER_IMAGE")

        # File Storage
        UPLOAD_DIR: str = Field(default="../backend/uploads", env="UPLOAD_DIR")
        TEMP_DIR: str = Field(default="./temp", env="TEMP_DIR")
        MAX_FILE_SIZE: int = Field(default=50000000, env="MAX_FILE_SIZE")  # 50MB

        # External Services
        NODEJS_BACKEND_URL: str = Field(default="http://localhost:5000", env="NODEJS_BACKEND_URL")
        NODEJS_API_KEY: str = Field(default="", env="NODEJS_API_KEY")

        # CORS
        ALLOWED_ORIGINS: str = Field(
            default="http://localhost:3000,http://localhost:3001,http://localhost:3002",
            env="ALLOWED_ORIGINS"
        )

        # Logging
        LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
        LOG_FILE: str = Field(default="logs/face_service.log", env="LOG_FILE")

        class Config:
            env_file = ".env"
            case_sensitive = True

        @property
        def allowed_origins_list(self) -> List[str]:
            """Convert comma-separated ALLOWED_ORIGINS string to list"""
            if isinstance(self.ALLOWED_ORIGINS, str):
                return [origin.strip() for origin in self.ALLOWED_ORIGINS.split(",")]
            return self.ALLOWED_ORIGINS

    # Create settings instance
    settings = Settings()

else:
    # Fallback settings class when pydantic is not available
    class FallbackSettings:
        DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5433/photocap")
        HOST = os.getenv("HOST", "0.0.0.0")
        PORT = int(os.getenv("PORT", "8000"))
        DEBUG = os.getenv("DEBUG", "True").lower() == "true"
        SECRET_KEY = os.getenv("SECRET_KEY", "fallback-secret-key-change-in-production")
        ALGORITHM = os.getenv("ALGORITHM", "HS256")
        ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
        FACE_RECOGNITION_TOLERANCE = float(os.getenv("FACE_RECOGNITION_TOLERANCE", "0.4"))
        MIN_FACE_SIZE = int(os.getenv("MIN_FACE_SIZE", "50"))
        MAX_FACES_PER_IMAGE = int(os.getenv("MAX_FACES_PER_IMAGE", "10"))
        UPLOAD_DIR = os.getenv("UPLOAD_DIR", "../backend/uploads")
        TEMP_DIR = os.getenv("TEMP_DIR", "./temp")
        MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", "50000000"))
        NODEJS_BACKEND_URL = os.getenv("NODEJS_BACKEND_URL", "http://localhost:5000")
        NODEJS_API_KEY = os.getenv("NODEJS_API_KEY", "")
        ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:3001,http://localhost:3002")
        LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
        LOG_FILE = os.getenv("LOG_FILE", "logs/face_service.log")

        @property
        def allowed_origins_list(self) -> List[str]:
            """Convert comma-separated ALLOWED_ORIGINS string to list"""
            return [origin.strip() for origin in self.ALLOWED_ORIGINS.split(",")]

    settings = FallbackSettings()

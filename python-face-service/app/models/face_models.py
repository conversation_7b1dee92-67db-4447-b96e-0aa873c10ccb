"""
Database models for face recognition data
"""

from sqlalchemy import <PERSON>umn, <PERSON>, Integer, Float, Boolean, DateTime, Text, ForeignKey, ARRAY, JSON
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.core.database import Base

class Event(Base):
    """Events table - corresponds to Node.js events"""
    __tablename__ = "events"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    studio_id = Column(String, nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text)
    is_public = Column(Boolean, default=True)
    unique_id = Column(String, unique=True, nullable=False)
    access_link = Column(String, nullable=False)
    qr_code = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    images = relationship("EventImage", back_populates="event", cascade="all, delete-orphan")
    visitors = relationship("EventVisitor", back_populates="event", cascade="all, delete-orphan")

class EventImage(Base):
    """Event images with face processing data"""
    __tablename__ = "event_images"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    event_id = Column(String, ForeignKey("events.id", ondelete="CASCADE"), nullable=False)
    filename = Column(String, nullable=False)
    original_name = Column(String, nullable=False)
    path = Column(String, nullable=False)
    size = Column(Integer, nullable=False)
    mime_type = Column(String, nullable=False)
    face_count = Column(Integer, default=0)
    processing_status = Column(String, default="pending")  # pending, processing, completed, failed
    uploaded_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    event = relationship("Event", back_populates="images")
    face_descriptors = relationship("FaceDescriptor", back_populates="image", cascade="all, delete-orphan")

class FaceDescriptor(Base):
    """Face descriptors extracted from images"""
    __tablename__ = "face_descriptors"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    image_id = Column(String, ForeignKey("event_images.id", ondelete="CASCADE"), nullable=False)
    descriptor_vector = Column(ARRAY(Float), nullable=False)  # 512-dimensional ArcFace encoding
    face_location = Column(JSONB)  # {top, right, bottom, left}
    confidence = Column(Float, default=0.0)
    face_landmarks = Column(JSONB)  # Optional: facial landmarks
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    image = relationship("EventImage", back_populates="face_descriptors")
    matches = relationship("VisitorMatch", back_populates="face_descriptor")

class EventVisitor(Base):
    """Visitors who accessed the event"""
    __tablename__ = "event_visitors"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    event_id = Column(String, ForeignKey("events.id", ondelete="CASCADE"), nullable=False)
    name = Column(String, nullable=False)
    contact = Column(String)  # Phone number
    email = Column(String)
    selfie_path = Column(String, nullable=False)
    selfie_descriptor = Column(ARRAY(Float), nullable=False)  # 128-dimensional face encoding
    processing_status = Column(String, default="completed")  # pending, processing, completed, failed
    visited_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    event = relationship("Event", back_populates="visitors")
    matches = relationship("VisitorMatch", back_populates="visitor", cascade="all, delete-orphan")

class VisitorMatch(Base):
    """Matched photos for visitors"""
    __tablename__ = "visitor_matches"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    visitor_id = Column(String, ForeignKey("event_visitors.id", ondelete="CASCADE"), nullable=False)
    image_id = Column(String, ForeignKey("event_images.id", ondelete="CASCADE"), nullable=False)
    face_descriptor_id = Column(String, ForeignKey("face_descriptors.id", ondelete="CASCADE"), nullable=False)
    confidence_score = Column(Float, nullable=False)
    match_threshold = Column(Float, default=0.6)
    matched_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    visitor = relationship("EventVisitor", back_populates="matches")
    image = relationship("EventImage")
    face_descriptor = relationship("FaceDescriptor", back_populates="matches")

class ProcessingJob(Base):
    """Track face processing jobs"""
    __tablename__ = "processing_jobs"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    event_id = Column(String, ForeignKey("events.id", ondelete="CASCADE"), nullable=False)
    job_type = Column(String, nullable=False)  # "image_processing", "face_matching"
    status = Column(String, default="pending")  # pending, processing, completed, failed
    total_items = Column(Integer, default=0)
    processed_items = Column(Integer, default=0)
    error_message = Column(Text)
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))
    
    # Progress tracking
    @property
    def progress_percentage(self):
        if self.total_items == 0:
            return 0
        return (self.processed_items / self.total_items) * 100

#!/bin/bash

# Production Face Recognition Setup Script
# This script sets up the production-ready face recognition system

set -e

echo "🚀 Setting up Production Face Recognition System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check system requirements
check_requirements() {
    print_step "Checking system requirements..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
    print_status "Python 3 found: $(python3 --version)"
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 16 or higher."
        exit 1
    fi
    print_status "Node.js found: $(node --version)"
    
    # Check PostgreSQL
    if ! command -v psql &> /dev/null; then
        print_warning "PostgreSQL client not found. Make sure PostgreSQL is installed and running."
    else
        print_status "PostgreSQL client found"
    fi
    
    # Check Redis (optional)
    if ! command -v redis-cli &> /dev/null; then
        print_warning "Redis not found. Install Redis for production caching and job queues."
    else
        print_status "Redis found"
    fi
}

# Setup Python environment
setup_python_env() {
    print_step "Setting up Python environment..."
    cd python-face-service
    
    # Create virtual environment
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_status "Virtual environment created"
    else
        print_status "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip
    
    # Install production dependencies
    print_status "Installing production dependencies..."
    pip install -r requirements.txt
    
    # Download InsightFace models
    print_status "Downloading InsightFace models (this may take a few minutes)..."
    python -c "
import insightface
try:
    app = insightface.app.FaceAnalysis(providers=['CPUExecutionProvider'])
    app.prepare(ctx_id=0, det_size=(640, 640))
    print('✅ InsightFace models downloaded successfully')
except Exception as e:
    print(f'⚠️  InsightFace setup failed: {e}')
    print('Will use fallback models')
"
    
    cd ..
}

# Setup Node.js backend
setup_backend() {
    print_step "Setting up Node.js backend..."
    cd backend
    
    # Install dependencies
    if [ ! -d "node_modules" ]; then
        print_status "Installing backend dependencies..."
        npm install
    else
        print_status "Backend dependencies already installed"
    fi
    
    # Generate Prisma client
    print_status "Generating Prisma client..."
    npm run db:generate
    
    cd ..
}

# Setup frontend applications
setup_frontend() {
    print_step "Setting up frontend applications..."
    
    # Admin panel
    cd admin
    if [ ! -d "node_modules" ]; then
        print_status "Installing admin panel dependencies..."
        npm install
    fi
    cd ..
    
    # Client panel
    cd client
    if [ ! -d "node_modules" ]; then
        print_status "Installing client panel dependencies..."
        npm install
    fi
    cd ..
    
    # Client portal
    cd client-portal
    if [ ! -d "node_modules" ]; then
        print_status "Installing client portal dependencies..."
        npm install
    fi
    cd ..
}

# Create production configuration
create_production_config() {
    print_step "Creating production configuration..."
    
    # Python service .env
    if [ ! -f "python-face-service/.env" ]; then
        print_status "Creating Python service .env file..."
        cp python-face-service/.env.example python-face-service/.env
        print_warning "Please update python-face-service/.env with your production settings"
    fi
    
    # Backend .env
    if [ ! -f "backend/.env" ]; then
        print_status "Creating backend .env file..."
        cat > backend/.env << EOF
# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/photocap_production"

# JWT
JWT_SECRET="your-super-secure-jwt-secret-change-this"
JWT_EXPIRES_IN="7d"

# Server
PORT=5000
NODE_ENV=production

# CORS
CORS_ORIGIN="http://localhost:3000,http://localhost:3001,http://localhost:3002"

# Face Recognition Service
PYTHON_FACE_SERVICE_URL="http://localhost:8000"

# File Upload
MAX_FILE_SIZE=50000000
UPLOAD_DIR="./uploads"

# Client Portal
CLIENT_PORTAL_URL="http://localhost:3002"
EOF
        print_warning "Please update backend/.env with your production settings"
    fi
}

# Run database migrations
run_migrations() {
    print_step "Running database migrations..."
    cd backend
    
    print_status "Pushing database schema..."
    npm run db:push
    
    print_status "Running database migrations..."
    npm run db:migrate || print_warning "Migration failed - database might not be running"
    
    cd ..
}

# Main setup function
main() {
    print_status "🎯 Production Face Recognition System Setup"
    print_status "This will set up a production-ready face recognition system with:"
    print_status "- RetinaFace + ArcFace for high accuracy"
    print_status "- 512-dimensional face embeddings"
    print_status "- Quality assessment and filtering"
    print_status "- Performance monitoring and error tracking"
    echo ""
    
    check_requirements
    setup_python_env
    setup_backend
    setup_frontend
    create_production_config
    run_migrations
    
    echo ""
    print_status "✅ Production Face Recognition System setup completed!"
    echo ""
    print_status "📋 Next Steps:"
    print_status "1. Update .env files with your production settings"
    print_status "2. Ensure PostgreSQL is running and accessible"
    print_status "3. Optional: Set up Redis for better performance"
    print_status "4. Start services individually or use process manager"
    echo ""
    print_status "🚀 To start services:"
    print_status "Backend: cd backend && npm run dev"
    print_status "Python: cd python-face-service && source venv/bin/activate && python start.py"
    print_status "Admin: cd admin && npm run dev"
    print_status "Client: cd client && npm run dev"
    print_status "Portal: cd client-portal && npm run dev"
    echo ""
    print_status "📚 Documentation:"
    print_status "- Read PRODUCTION_FACE_RECOGNITION_GUIDE.md for detailed setup"
    print_status "- Check FACE_RECOGNITION_SYSTEM.md for API documentation"
    echo ""
    print_warning "⚠️  Important: This is a production setup. Make sure to:"
    print_warning "- Use strong passwords and secrets"
    print_warning "- Enable SSL/HTTPS in production"
    print_warning "- Set up proper monitoring and backups"
    print_warning "- Review security settings before deployment"
}

# Run main function
main

# Root level .gitignore for FotoOwl-style Face Recognition System

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
logs/

# Environment variables (sensitive)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies (each project has its own)
node_modules/
venv/
env/
.venv/

# Build outputs
dist/
build/
*.tsbuildinfo

# Database files (sensitive)
*.db
*.sqlite
*.sqlite3

# Uploads and user data (SENSITIVE - contains photos)
uploads/
temp/
selfies/
media/
face_data/
processed_images/

# Face Recognition Models and Data (sensitive)
models/
*.model
*.pkl
*.npy
*.joblib
*.h5
*.hdf5
face_encodings/
known_faces/
unknown_faces/
training_data/

# Test files
test-*.js
test-*.ts
*.test.js
*.test.ts

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Cache directories
.cache/
.parcel-cache/
.npm/
__pycache__/
*.py[cod]
*$py.class

# Backup files
*.backup
*.bak
*.orig

# Security files (NEVER commit these)
*.pem
*.key
*.crt
*.p12
secrets/
keys/
certificates/

# Temporary files
*.tmp
*.temp
processing_*

# Coverage reports
coverage/
.nyc_output
.pytest_cache/

# Documentation build
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# Project specific
create-test-client.js
create-studio-user.js
test-face-processing.js

# Prisma migrations (optional - uncomment if you don't want to track migrations)
# prisma/migrations/

# Production deployment files
docker-compose.override.yml
.dockerignore

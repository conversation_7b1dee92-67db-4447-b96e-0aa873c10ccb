'use client';

import { useEffect, useState } from 'react';
import { X, Users, Mail, Phone, Calendar, Camera, Eye, Download } from 'lucide-react';
import { clientService } from '@/lib/services';
import { Client, Image } from '@/types';
import toast from 'react-hot-toast';

interface ViewClientModalProps {
  client: Client;
  onClose: () => void;
}

export default function ViewClientModal({ client, onClose }: ViewClientModalProps) {
  const [images, setImages] = useState<Image[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState<Image | null>(null);

  useEffect(() => {
    fetchClientImages();
  }, []);

  const fetchClientImages = async () => {
    try {
      const data = await clientService.getClientImages(client.id);
      setImages(data);
    } catch (error) {
      toast.error('Failed to fetch client images');
    } finally {
      setIsLoading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const downloadImage = (image: Image, quality: 'original' | 'optimized' = 'original') => {
    const link = document.createElement('a');
    link.href = `${process.env.NEXT_PUBLIC_API_URL}/download/image/${image.id}?quality=${quality}`;
    link.download = image.originalName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center">
              <Users className="h-6 w-6 text-primary-600 mr-2" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Client Details</h2>
                <p className="text-sm text-gray-600">{client.name}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <div className="p-6 space-y-6">
            {/* Client Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Client Information</h3>
                
                <div className="space-y-3">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{client.name}</p>
                      <p className="text-xs text-gray-500">Client ID: {client.uniqueId}</p>
                    </div>
                  </div>

                  {client.email && (
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-900">{client.email}</span>
                    </div>
                  )}

                  {client.phone && (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-900">{client.phone}</span>
                    </div>
                  )}

                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm text-gray-900">
                        Created: {new Date(client.createdAt).toLocaleDateString()}
                      </p>
                      {client.lastLogin && (
                        <p className="text-xs text-green-600">
                          Last login: {new Date(client.lastLogin).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${
                      client.isActive ? 'bg-green-500' : 'bg-red-500'
                    }`} />
                    <span className={`text-sm font-medium ${
                      client.isActive ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {client.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Access Information</h3>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-gray-700 mb-2">Access Link:</p>
                  <p className="text-xs text-gray-600 break-all">{client.accessLink}</p>
                </div>

                <div className="grid grid-cols-2 gap-4 text-center">
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <Camera className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                    <p className="text-lg font-bold text-blue-900">{images.length}</p>
                    <p className="text-xs text-blue-700">Total Images</p>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg">
                    <Download className="h-6 w-6 text-green-600 mx-auto mb-1" />
                    <p className="text-lg font-bold text-green-900">
                      {formatFileSize(images.reduce((total, img) => total + img.size, 0))}
                    </p>
                    <p className="text-xs text-green-700">Total Size</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Images Gallery */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Images Gallery</h3>
              
              {isLoading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                </div>
              ) : images.length > 0 ? (
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 max-h-96 overflow-y-auto">
                  {images.map((image) => (
                    <div key={image.id} className="relative group">
                      <img
                        src={`${process.env.NEXT_PUBLIC_API_URL?.replace('/api', '')}/${image.path}`}
                        alt={image.originalName}
                        className="w-full h-24 object-cover rounded cursor-pointer hover:opacity-75 transition-opacity"
                        onClick={() => setSelectedImage(image)}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all rounded flex items-center justify-center">
                        <Eye className="h-5 w-5 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                      <div className="absolute bottom-1 right-1 bg-black bg-opacity-75 text-white text-xs px-1 rounded">
                        {formatFileSize(image.size)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No images uploaded yet</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Image Preview Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-60">
          <div className="max-w-4xl max-h-[90vh] p-4">
            <div className="relative">
              <img
                src={`${process.env.NEXT_PUBLIC_API_URL?.replace('/api', '')}/${selectedImage.path}`}
                alt={selectedImage.originalName}
                className="max-w-full max-h-[80vh] object-contain"
              />
              <button
                onClick={() => setSelectedImage(null)}
                className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75"
              >
                <X className="h-5 w-5" />
              </button>
              <button
                onClick={() => downloadImage(selectedImage)}
                className="absolute top-2 left-2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75"
              >
                <Download className="h-5 w-5" />
              </button>
            </div>
            <div className="text-center mt-4">
              <p className="text-white text-sm">{selectedImage.originalName}</p>
              {selectedImage.description && (
                <p className="text-gray-300 text-xs mt-1">{selectedImage.description}</p>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

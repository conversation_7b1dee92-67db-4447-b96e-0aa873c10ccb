'use client';

import { useState } from 'react';
import { X, Download, Copy, RefreshCw, QrCode } from 'lucide-react';
import { clientService } from '@/lib/services';
import { Client } from '@/types';
import toast from 'react-hot-toast';

interface QRCodeModalProps {
  client: Client;
  onClose: () => void;
}

export default function QRCodeModal({ client, onClose }: QRCodeModalProps) {
  const [currentClient, setCurrentClient] = useState(client);
  const [isRegenerating, setIsRegenerating] = useState(false);

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const downloadQRCode = () => {
    if (!currentClient.qrCode) return;
    
    const link = document.createElement('a');
    link.href = currentClient.qrCode;
    link.download = `${currentClient.name}-qr-code.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('QR code downloaded');
  };

  const regenerateQRCode = async () => {
    setIsRegenerating(true);
    try {
      const updatedClient = await clientService.regenerateQRCode(currentClient.id);
      setCurrentClient(updatedClient);
      toast.success('QR code regenerated successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to regenerate QR code');
    } finally {
      setIsRegenerating(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <QrCode className="h-6 w-6 text-primary-600 mr-2" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">QR Code</h2>
              <p className="text-sm text-gray-600">{currentClient.name}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Client Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-3">Client Information</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Name:</span>
                <span className="font-medium">{currentClient.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Client ID:</span>
                <span className="font-medium">{currentClient.uniqueId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className={`font-medium ${
                  currentClient.isActive ? 'text-green-600' : 'text-red-600'
                }`}>
                  {currentClient.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </div>

          {/* QR Code Display */}
          <div className="text-center">
            <h3 className="font-medium text-gray-900 mb-4">QR Code</h3>
            {currentClient.qrCode ? (
              <div className="inline-block p-6 bg-white border-2 border-gray-200 rounded-lg shadow-sm">
                <img
                  src={currentClient.qrCode}
                  alt="Client QR Code"
                  className="w-64 h-64 mx-auto"
                />
              </div>
            ) : (
              <div className="inline-block p-6 bg-gray-100 border-2 border-gray-200 rounded-lg">
                <QrCode className="w-64 h-64 mx-auto text-gray-400" />
                <p className="text-gray-500 mt-2">QR Code not available</p>
              </div>
            )}
          </div>

          {/* Access Link */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Access Link
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={currentClient.accessLink}
                readOnly
                className="flex-1 input-field bg-gray-50 text-sm"
              />
              <button
                onClick={() => copyToClipboard(currentClient.accessLink, 'Access Link')}
                className="p-2 text-gray-500 hover:text-gray-700 border border-gray-300 rounded"
                title="Copy Link"
              >
                <Copy className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">How to use:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Share the QR code with your client</li>
              <li>• Client can scan the QR code with their phone camera</li>
              <li>• Or share the access link directly</li>
              <li>• Client will be redirected to their personal gallery</li>
            </ul>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-3">
            {currentClient.qrCode && (
              <button
                onClick={downloadQRCode}
                className="btn-primary flex items-center justify-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>Download QR Code</span>
              </button>
            )}
            
            <button
              onClick={regenerateQRCode}
              disabled={isRegenerating}
              className="btn-secondary flex items-center justify-center space-x-2 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 ${isRegenerating ? 'animate-spin' : ''}`} />
              <span>{isRegenerating ? 'Regenerating...' : 'Regenerate QR'}</span>
            </button>
          </div>

          {/* Print Instructions */}
          <div className="text-center pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              Tip: You can print this QR code and give it to your client for easy access
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Camera, Upload, MapPin, Clock, Globe } from 'lucide-react';
import { settingsService } from '@/lib/services';
import { StudioProfile, StudioSettings } from '@/types';
import toast from 'react-hot-toast';

interface StudioTabProps {
  profile: StudioProfile;
  settings: StudioSettings;
  onProfileUpdate: (profile: StudioProfile) => void;
  onSettingsUpdate: (settings: StudioSettings) => void;
}

interface StudioSettingsData {
  businessAddress: string;
  operatingHours: string;
  socialLinks: {
    website?: string;
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
}

export default function StudioTab({ 
  profile, 
  settings, 
  onProfileUpdate, 
  onSettingsUpdate 
}: StudioTabProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<StudioSettingsData>({
    defaultValues: {
      businessAddress: settings?.businessAddress || '',
      operatingHours: settings?.operatingHours || '',
      socialLinks: settings?.socialLinks || {},
    },
  });

  const onSubmit = async (data: StudioSettingsData) => {
    setIsLoading(true);
    try {
      const updatedSettings = await settingsService.updateProfileSettings(data);
      onSettingsUpdate(updatedSettings);
      toast.success('Studio settings updated successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to update studio settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size should be less than 5MB');
      return;
    }

    setIsUploadingLogo(true);
    try {
      const result = await settingsService.uploadLogo(file);
      const updatedSettings = await settingsService.updateProfileSettings({
        logoUrl: result.url,
      });
      onSettingsUpdate(updatedSettings);
      toast.success('Logo updated successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to upload logo');
    } finally {
      setIsUploadingLogo(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <Camera className="h-6 w-6 text-primary-600 mr-2" />
        <h2 className="text-xl font-semibold text-gray-900">Studio Settings</h2>
      </div>

      <div className="space-y-8">
        {/* Studio Logo Section */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Studio Branding</h3>
          
          <div className="flex items-center space-x-6">
            <div className="relative">
              <div className="w-24 h-24 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                {settings?.logoUrl ? (
                  <img
                    src={settings.logoUrl}
                    alt="Studio Logo"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <Camera className="h-12 w-12 text-gray-400" />
                )}
              </div>
              {isUploadingLogo && (
                <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                </div>
              )}
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-900">Studio Logo</h4>
              <p className="text-sm text-gray-600 mb-3">
                Upload your studio logo. This will be shown to clients.
              </p>
              <label className="btn-secondary cursor-pointer inline-flex items-center space-x-2">
                <Upload className="h-4 w-4" />
                <span>{isUploadingLogo ? 'Uploading...' : 'Upload Logo'}</span>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="hidden"
                  disabled={isUploadingLogo}
                />
              </label>
            </div>
          </div>
        </div>

        {/* Studio Information Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <MapPin className="h-5 w-5 text-gray-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Location & Hours</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Business Address
                </label>
                <textarea
                  {...register('businessAddress')}
                  rows={3}
                  className="input-field"
                  placeholder="Enter your complete business address"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Operating Hours
                </label>
                <textarea
                  {...register('operatingHours')}
                  rows={3}
                  className="input-field"
                  placeholder="e.g., Mon-Fri: 9:00 AM - 6:00 PM, Sat: 10:00 AM - 4:00 PM, Sun: Closed"
                />
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <Globe className="h-5 w-5 text-gray-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Social Media Links</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Website
                </label>
                <input
                  {...register('socialLinks.website')}
                  type="url"
                  className="input-field"
                  placeholder="https://your-website.com"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Facebook
                </label>
                <input
                  {...register('socialLinks.facebook')}
                  type="url"
                  className="input-field"
                  placeholder="https://facebook.com/your-page"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Instagram
                </label>
                <input
                  {...register('socialLinks.instagram')}
                  type="url"
                  className="input-field"
                  placeholder="https://instagram.com/your-account"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Twitter
                </label>
                <input
                  {...register('socialLinks.twitter')}
                  type="url"
                  className="input-field"
                  placeholder="https://twitter.com/your-account"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end pt-4 border-t border-gray-200">
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary disabled:opacity-50"
            >
              {isLoading ? 'Updating...' : 'Update Studio Settings'}
            </button>
          </div>
        </form>

        {/* Studio Information Display */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Current Studio Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Studio Name:</span>
              <p className="text-gray-900">{profile.name}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Email:</span>
              <p className="text-gray-900">{profile.email}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Phone:</span>
              <p className="text-gray-900">{profile.phone || 'Not set'}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Address:</span>
              <p className="text-gray-900">{profile.address || 'Not set'}</p>
            </div>
          </div>
          {profile.description && (
            <div className="mt-4">
              <span className="font-medium text-gray-700">Description:</span>
              <p className="text-gray-900 mt-1">{profile.description}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

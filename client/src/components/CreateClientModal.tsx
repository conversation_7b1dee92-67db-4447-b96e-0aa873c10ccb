'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { X, Users, Copy, Download } from 'lucide-react';
import { clientService } from '@/lib/services';
import { CreateClientRequest, Client } from '@/types';
import toast from 'react-hot-toast';

interface CreateClientModalProps {
  onClose: () => void;
  onClientCreated: () => void;
}

interface ClientWithPassword extends Client {
  plainPassword?: string;
}

export default function CreateClientModal({ onClose, onClientCreated }: CreateClientModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [createdClient, setCreatedClient] = useState<ClientWithPassword | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CreateClientRequest>();

  const onSubmit = async (data: CreateClientRequest) => {
    setIsLoading(true);
    try {
      const client = await clientService.createClient(data) as ClientWithPassword;
      setCreatedClient(client);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to create client');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const downloadQRCode = () => {
    if (!createdClient?.qrCode) return;
    
    const link = document.createElement('a');
    link.href = createdClient.qrCode;
    link.download = `${createdClient.name}-qr-code.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('QR code downloaded');
  };

  const handleFinish = () => {
    onClientCreated();
    onClose();
  };

  if (createdClient) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Client Created Successfully!</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <div className="p-6 space-y-6">
            <div className="text-center">
              <div className="bg-green-100 p-3 rounded-full w-16 h-16 mx-auto mb-4">
                <Users className="h-10 w-10 text-green-600 mx-auto mt-1" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">{createdClient.name}</h3>
              <p className="text-gray-600">Client has been created successfully</p>
            </div>

            {/* Client Credentials */}
            <div className="bg-gray-50 p-4 rounded-lg space-y-4">
              <h4 className="font-medium text-gray-900">Client Login Credentials</h4>
              
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Client ID
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={createdClient.uniqueId}
                      readOnly
                      className="flex-1 input-field bg-white"
                    />
                    <button
                      onClick={() => copyToClipboard(createdClient.uniqueId, 'Client ID')}
                      className="p-2 text-gray-500 hover:text-gray-700"
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Password
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={createdClient.plainPassword || ''}
                      readOnly
                      className="flex-1 input-field bg-white"
                    />
                    <button
                      onClick={() => copyToClipboard(createdClient.plainPassword || '', 'Password')}
                      className="p-2 text-gray-500 hover:text-gray-700"
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Access Link
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={createdClient.accessLink}
                      readOnly
                      className="flex-1 input-field bg-white text-xs"
                    />
                    <button
                      onClick={() => copyToClipboard(createdClient.accessLink, 'Access Link')}
                      className="p-2 text-gray-500 hover:text-gray-700"
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* QR Code */}
            {createdClient.qrCode && (
              <div className="text-center">
                <h4 className="font-medium text-gray-900 mb-3">QR Code</h4>
                <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
                  <img
                    src={createdClient.qrCode}
                    alt="Client QR Code"
                    className="w-48 h-48"
                  />
                </div>
                <div className="mt-3">
                  <button
                    onClick={downloadQRCode}
                    className="btn-secondary flex items-center space-x-2 mx-auto"
                  >
                    <Download className="h-4 w-4" />
                    <span>Download QR Code</span>
                  </button>
                </div>
              </div>
            )}

            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Important:</strong> Save these credentials securely. The password cannot be retrieved later, only reset.
              </p>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleFinish}
                className="btn-primary"
              >
                Finish
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Create New Client</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Client Name *
            </label>
            <input
              {...register('name', {
                required: 'Client name is required',
                minLength: { value: 2, message: 'Name must be at least 2 characters' },
              })}
              className="input-field"
              placeholder="Enter client name"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <input
              {...register('email', {
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address',
                },
              })}
              type="email"
              className="input-field"
              placeholder="Enter email address (optional)"
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Phone Number
            </label>
            <input
              {...register('phone')}
              type="tel"
              className="input-field"
              placeholder="Enter phone number (optional)"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary disabled:opacity-50"
            >
              {isLoading ? 'Creating...' : 'Create Client'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

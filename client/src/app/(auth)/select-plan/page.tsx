'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  Check, 
  X, 
  Crown, 
  Zap, 
  Star, 
  Users, 
  HardDrive, 
  Camera,
  Shield,
  ArrowRight
} from 'lucide-react';
import { subscriptionService } from '@/lib/services';
import { SubscriptionPlan } from '@/types';
import toast from 'react-hot-toast';

export default function SelectPlanPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);

  // Check if user came from registration
  const fromRegistration = searchParams.get('from') === 'registration';

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      const data = await subscriptionService.getAllPlans();
      setPlans(data);
      
      // Auto-select free plan
      const freePlan = data.find(plan => plan.type === 'FREE');
      if (freePlan) {
        setSelectedPlan(freePlan.id);
      }
    } catch (error) {
      toast.error('Failed to fetch subscription plans');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePlanSelect = async () => {
    if (!selectedPlan || isCreating) return;
    
    setIsCreating(true);

    try {
      await subscriptionService.createSubscription(selectedPlan);
      toast.success('Plan selected successfully!');
      router.push('/dashboard');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to select plan');
    } finally {
      setIsCreating(false);
    }
  };

  const getPlanIcon = (type: string) => {
    switch (type) {
      case 'FREE':
        return <Star className="h-6 w-6" />;
      case 'BASIC':
        return <Zap className="h-6 w-6" />;
      case 'PREMIUM':
        return <Crown className="h-6 w-6" />;
      case 'ENTERPRISE':
        return <Shield className="h-6 w-6" />;
      default:
        return <Star className="h-6 w-6" />;
    }
  };

  const getPlanColor = (type: string) => {
    switch (type) {
      case 'FREE':
        return 'border-gray-300 bg-white';
      case 'BASIC':
        return 'border-blue-300 bg-blue-50';
      case 'PREMIUM':
        return 'border-purple-300 bg-purple-50';
      case 'ENTERPRISE':
        return 'border-gold-300 bg-gold-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900">Choose Your Plan</h1>
          <p className="text-gray-600 mt-2">
            {fromRegistration 
              ? 'Welcome! Select a plan to get started with your photography studio.'
              : 'Select the perfect plan for your photography studio.'
            }
          </p>
          {fromRegistration && (
            <div className="mt-4 p-4 bg-green-50 rounded-lg inline-block">
              <p className="text-sm text-green-800">
                🎉 Registration successful! Your account is pending admin approval.
              </p>
            </div>
          )}
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {plans.map((plan) => (
            <div
              key={plan.id}
              onClick={() => setSelectedPlan(plan.id)}
              className={`relative rounded-xl border-2 p-6 cursor-pointer transition-all duration-300 hover:shadow-lg ${
                selectedPlan === plan.id
                  ? 'border-primary-500 bg-primary-50 shadow-lg'
                  : getPlanColor(plan.type)
              } ${plan.type === 'PREMIUM' ? 'ring-2 ring-purple-500 ring-opacity-30' : ''}`}
            >
              {plan.type === 'PREMIUM' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                    Popular
                  </span>
                </div>
              )}

              {/* Selection indicator */}
              {selectedPlan === plan.id && (
                <div className="absolute top-4 right-4">
                  <div className="bg-primary-500 text-white rounded-full p-1">
                    <Check className="h-4 w-4" />
                  </div>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-6">
                <div className="inline-flex p-2 rounded-full bg-white shadow-sm mb-3">
                  {getPlanIcon(plan.type)}
                </div>
                <h3 className="text-lg font-bold text-gray-900">{plan.name}</h3>
                <div className="mt-2">
                  <span className="text-3xl font-bold text-gray-900">${plan.price}</span>
                  <span className="text-gray-600">/{plan.billingCycle}</span>
                </div>
                {plan.trialDays > 0 && (
                  <p className="text-sm text-green-600 mt-1">{plan.trialDays} days free</p>
                )}
              </div>

              {/* Key Features */}
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <Users className="h-4 w-4 text-gray-400 mr-2" />
                  <span>{plan.maxClients === 0 ? 'Unlimited' : plan.maxClients} clients</span>
                </div>
                
                <div className="flex items-center text-sm">
                  <HardDrive className="h-4 w-4 text-gray-400 mr-2" />
                  <span>{plan.maxStorageGB}GB storage</span>
                </div>
                
                <div className="flex items-center text-sm">
                  <Camera className="h-4 w-4 text-gray-400 mr-2" />
                  <span>
                    {plan.maxImagesPerClient === 0 ? 'Unlimited' : plan.maxImagesPerClient} images/client
                  </span>
                </div>

                {/* Premium features */}
                {(plan.customBranding || plan.advancedAnalytics || plan.prioritySupport) && (
                  <div className="pt-3 border-t border-gray-200">
                    {plan.customBranding && (
                      <div className="flex items-center text-xs text-gray-600 mb-1">
                        <Check className="h-3 w-3 text-green-500 mr-1" />
                        Custom Branding
                      </div>
                    )}
                    {plan.advancedAnalytics && (
                      <div className="flex items-center text-xs text-gray-600 mb-1">
                        <Check className="h-3 w-3 text-green-500 mr-1" />
                        Advanced Analytics
                      </div>
                    )}
                    {plan.prioritySupport && (
                      <div className="flex items-center text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500 mr-1" />
                        Priority Support
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="text-center">
          <button
            onClick={handlePlanSelect}
            disabled={!selectedPlan || isCreating}
            className={`inline-flex items-center px-8 py-3 rounded-lg font-medium transition-colors ${
              !selectedPlan || isCreating
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
          >
            {isCreating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Setting up your plan...
              </>
            ) : (
              <>
                Continue with Selected Plan
                <ArrowRight className="h-4 w-4 ml-2" />
              </>
            )}
          </button>
          
          <p className="text-sm text-gray-500 mt-4">
            You can change your plan anytime from the dashboard
          </p>
        </div>
      </div>
    </div>
  );
}

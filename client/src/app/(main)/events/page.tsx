'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Calendar, Users, Image as ImageIcon, QrCode, ExternalLink, Eye, EyeOff, Trash2, Edit } from 'lucide-react';
import { eventService } from '@/lib/services';
import { Event } from '@/types';
import toast from 'react-hot-toast';

export default function EventsPage() {
  const [events, setEvents] = useState<Event[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const router = useRouter();

  useEffect(() => {
    loadEvents();
  }, []);

  const loadEvents = async () => {
    try {
      setIsLoading(true);
      const data = await eventService.getStudioEvents();
      setEvents(data);
    } catch (error: any) {
      toast.error('Failed to load events');
      console.error('Error loading events:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateEvent = () => {
    setShowCreateModal(true);
  };

  const handleDeleteEvent = async (eventId: string, eventName: string) => {
    if (!confirm(`Are you sure you want to delete "${eventName}"? This will permanently delete all photos and visitor data.`)) {
      return;
    }

    try {
      await eventService.deleteEvent(eventId);
      toast.success('Event deleted successfully');
      loadEvents();
    } catch (error: any) {
      toast.error('Failed to delete event');
      console.error('Error deleting event:', error);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Events</h1>
          <p className="text-gray-600">Manage your photo events and share them with visitors</p>
        </div>
        <button
          onClick={handleCreateEvent}
          className="btn-primary flex items-center gap-2"
        >
          <Plus className="h-5 w-5" />
          Create Event
        </button>
      </div>

      {/* Events Grid */}
      {events.length === 0 ? (
        <div className="text-center py-12">
          <Calendar className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No events yet</h3>
          <p className="text-gray-600 mb-6">Create your first event to start sharing photos with visitors</p>
          <button
            onClick={handleCreateEvent}
            className="btn-primary flex items-center gap-2 mx-auto"
          >
            <Plus className="h-5 w-5" />
            Create Your First Event
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {events.map((event) => (
            <EventCard
              key={event.id}
              event={event}
              onDelete={handleDeleteEvent}
              onCopyLink={copyToClipboard}
              onViewDetails={(eventId) => router.push(`/events/${eventId}`)}
            />
          ))}
        </div>
      )}

      {/* Create Event Modal */}
      {showCreateModal && (
        <CreateEventModal
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false);
            loadEvents();
          }}
        />
      )}
    </div>
  );
}

interface EventCardProps {
  event: Event;
  onDelete: (eventId: string, eventName: string) => void;
  onCopyLink: (link: string, label: string) => void;
  onViewDetails: (eventId: string) => void;
}

function EventCard({ event, onDelete, onCopyLink, onViewDetails }: EventCardProps) {
  const [showQR, setShowQR] = useState(false);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      {/* Event Header */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">{event.name}</h3>
          {event.description && (
            <p className="text-sm text-gray-600 line-clamp-2">{event.description}</p>
          )}
        </div>
        <div className="flex items-center gap-2 ml-4">
          <span className={`px-2 py-1 text-xs rounded-full ${
            event.isPublic 
              ? 'bg-green-100 text-green-800' 
              : 'bg-yellow-100 text-yellow-800'
          }`}>
            {event.isPublic ? 'Public' : 'Private'}
          </span>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <ImageIcon className="h-5 w-5 text-gray-600 mx-auto mb-1" />
          <div className="text-lg font-semibold text-gray-900">{event._count?.images || 0}</div>
          <div className="text-xs text-gray-600">Photos</div>
        </div>
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <Users className="h-5 w-5 text-gray-600 mx-auto mb-1" />
          <div className="text-lg font-semibold text-gray-900">{event._count?.visitors || 0}</div>
          <div className="text-xs text-gray-600">Visitors</div>
        </div>
      </div>

      {/* Actions */}
      <div className="space-y-2">
        <button
          onClick={() => onViewDetails(event.id)}
          className="w-full btn-primary text-sm py-2"
        >
          View Details
        </button>
        
        <div className="flex gap-2">
          <button
            onClick={() => onCopyLink(event.accessLink, 'Event link')}
            className="flex-1 btn-secondary text-sm py-2 flex items-center justify-center gap-1"
          >
            <ExternalLink className="h-4 w-4" />
            Copy Link
          </button>
          
          <button
            onClick={() => setShowQR(!showQR)}
            className="btn-secondary text-sm py-2 px-3"
          >
            <QrCode className="h-4 w-4" />
          </button>
          
          <button
            onClick={() => onDelete(event.id, event.name)}
            className="btn-secondary text-sm py-2 px-3 text-red-600 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* QR Code */}
      {showQR && event.qrCode && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg text-center">
          <img 
            src={event.qrCode} 
            alt="Event QR Code" 
            className="mx-auto mb-2 max-w-32 max-h-32"
          />
          <p className="text-xs text-gray-600">Scan to access event</p>
        </div>
      )}

      {/* Event Info */}
      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="flex justify-between items-center text-xs text-gray-500">
          <span>ID: {event.uniqueId}</span>
          <span>{new Date(event.createdAt).toLocaleDateString()}</span>
        </div>
      </div>
    </div>
  );
}

interface CreateEventModalProps {
  onClose: () => void;
  onSuccess: () => void;
}

function CreateEventModal({ onClose, onSuccess }: CreateEventModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPublic: true,
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Event name is required');
      return;
    }

    try {
      setIsLoading(true);
      await eventService.createEvent(formData);
      toast.success('Event created successfully');
      onSuccess();
    } catch (error: any) {
      toast.error('Failed to create event');
      console.error('Error creating event:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-semibold mb-4">Create New Event</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Event Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="input-field"
              placeholder="e.g., Wedding Reception, Birthday Party"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="input-field"
              rows={3}
              placeholder="Optional description for your event"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="isPublic"
              checked={formData.isPublic}
              onChange={(e) => setFormData({ ...formData, isPublic: e.target.checked })}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor="isPublic" className="ml-2 block text-sm text-gray-700">
              Make this event publicly accessible
            </label>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 btn-secondary"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 btn-primary"
              disabled={isLoading}
            >
              {isLoading ? 'Creating...' : 'Create Event'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

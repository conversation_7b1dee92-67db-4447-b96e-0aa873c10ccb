'use client';

import { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { 
  ArrowLeft, Upload, Image as ImageIcon, Users, QrCode, 
  ExternalLink, BarChart3, Download, Trash2, Eye, Settings 
} from 'lucide-react';
import { eventService } from '@/lib/services';
import { Event, EventImage, EventStats } from '@/types';
import toast from 'react-hot-toast';

export default function EventDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const eventId = params.eventId as string;
  
  const [event, setEvent] = useState<Event | null>(null);
  const [images, setImages] = useState<EventImage[]>([]);
  const [stats, setStats] = useState<EventStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [showQR, setShowQR] = useState(false);
  const [previousCompletedCount, setPreviousCompletedCount] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (eventId) {
      loadEventData();
    }
  }, [eventId]);

  // Auto-refresh when images are processing
  useEffect(() => {
    const hasProcessingImages = images.some(img =>
      img.processingStatus === 'processing' || img.processingStatus === 'pending'
    );

    if (hasProcessingImages) {
      const interval = setInterval(() => {
        loadEventData();
      }, 3000); // Refresh every 3 seconds

      return () => clearInterval(interval);
    }
  }, [images, eventId]);

  const loadEventData = async () => {
    try {
      setIsLoading(true);
      const [eventData, imagesData, statsData] = await Promise.all([
        eventService.getEventById(eventId),
        eventService.getEventImages(eventId),
        eventService.getEventStats(eventId).catch(() => null), // Stats might fail if Python service is down
      ]);
      
      setEvent(eventData);

      // Check if processing completed
      const completedCount = imagesData.filter(img => img.processingStatus === 'completed').length;
      if (previousCompletedCount > 0 && completedCount > previousCompletedCount) {
        const newlyCompleted = completedCount - previousCompletedCount;
        toast.success(`${newlyCompleted} more images processed successfully!`);
      }
      setPreviousCompletedCount(completedCount);

      setImages(imagesData);
      setStats(statsData);
    } catch (error: any) {
      toast.error('Failed to load event data');
      console.error('Error loading event:', error);
      router.push('/events');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = async (files: FileList) => {
    if (!files.length) return;

    const validFiles = Array.from(files).filter(file => 
      file.type.startsWith('image/')
    );

    if (validFiles.length !== files.length) {
      toast.error('Only image files are allowed');
    }

    if (validFiles.length === 0) return;

    try {
      setIsUploading(true);

      // Show initial upload progress
      toast.loading(`Uploading ${validFiles.length} images...`, { id: 'upload-progress' });

      const uploadedImages = await eventService.uploadEventImages(eventId, validFiles);

      // Update toast to show face processing started
      toast.success(
        `🎉 ${uploadedImages.length} images uploaded successfully! Face recognition processing started...`,
        { id: 'upload-progress', duration: 4000 }
      );

      // Reload data immediately to show processing status
      loadEventData();

      // Set up polling to check processing status
      const checkProcessingStatus = async () => {
        try {
          const updatedEvent = await eventService.getEventById(eventId);
          const processingImages = updatedEvent.images.filter(img => img.processingStatus === 'processing');
          const completedImages = updatedEvent.images.filter(img => img.processingStatus === 'completed');

          if (processingImages.length === 0) {
            // All processing complete
            const totalFaces = completedImages.reduce((sum, img) => sum + (img.faceCount || 0), 0);
            toast.success(
              `✨ Face processing complete! Found ${totalFaces} faces in ${completedImages.length} images.`,
              { duration: 5000 }
            );
            loadEventData(); // Final reload
            return true; // Stop polling
          }

          // Still processing, continue polling
          return false;
        } catch (error) {
          console.error('Error checking processing status:', error);
          return true; // Stop polling on error
        }
      };

      // Poll every 3 seconds for up to 2 minutes
      let pollCount = 0;
      const maxPolls = 40; // 2 minutes
      const pollInterval = setInterval(async () => {
        pollCount++;
        const shouldStop = await checkProcessingStatus();

        if (shouldStop || pollCount >= maxPolls) {
          clearInterval(pollInterval);
          if (pollCount >= maxPolls) {
            toast.info('Face processing is taking longer than expected. Please refresh the page to check status.');
          }
        }
      }, 3000);

    } catch (error: any) {
      toast.error('Failed to upload images');
      console.error('Error uploading images:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const handleStartProcessing = async () => {
    if (!event || images.length === 0) {
      toast.error('No images to process');
      return;
    }

    try {
      setIsUploading(true);

      const response = await fetch(`/api/events/${eventId}/reprocess`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Face processing started! Images will be updated shortly.');
        // Reload data to show updated processing status
        setTimeout(() => {
          loadEventData();
        }, 2000);
      } else {
        toast.error(result.message || 'Failed to start processing');
      }
    } catch (error: any) {
      toast.error('Failed to start processing');
      console.error('Error starting processing:', error);
    } finally {
      setIsUploading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900">Event not found</h2>
        <button onClick={() => router.push('/events')} className="btn-primary mt-4">
          Back to Events
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.push('/events')}
            className="btn-secondary p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{event.name}</h1>
            <p className="text-gray-600">{event.description || 'No description'}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <span className={`px-3 py-1 text-sm rounded-full ${
            event.isPublic 
              ? 'bg-green-100 text-green-800' 
              : 'bg-yellow-100 text-yellow-800'
          }`}>
            {event.isPublic ? 'Public' : 'Private'}
          </span>
          <button
            onClick={() => setShowQR(!showQR)}
            className="btn-secondary flex items-center gap-2"
          >
            <QrCode className="h-4 w-4" />
            QR Code
          </button>
        </div>
      </div>

      {/* QR Code Modal */}
      {showQR && event.qrCode && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-4">Event QR Code</h3>
              <img 
                src={event.qrCode} 
                alt="Event QR Code" 
                className="mx-auto mb-4 max-w-64 max-h-64"
              />
              <p className="text-sm text-gray-600 mb-4">
                Visitors can scan this code to access the event
              </p>
              <div className="flex gap-2">
                <button
                  onClick={() => copyToClipboard(event.accessLink, 'Event link')}
                  className="flex-1 btn-secondary text-sm"
                >
                  Copy Link
                </button>
                <button
                  onClick={() => setShowQR(false)}
                  className="flex-1 btn-primary text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <ImageIcon className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Photos</p>
              <p className="text-2xl font-semibold text-gray-900">{images.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Visitors</p>
              <p className="text-2xl font-semibold text-gray-900">{stats?.totalVisitors || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <Eye className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Faces Detected</p>
              <p className="text-2xl font-semibold text-gray-900">{stats?.totalFaces || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <BarChart3 className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Processing Status</p>
              <div className="flex items-center gap-2">
                <p className="text-sm font-semibold text-gray-900">
                  {images.filter(img => img.processingStatus === 'completed').length} / {images.length} Complete
                </p>
                {images.some(img => img.processingStatus === 'processing') && (
                  <div className="flex items-center gap-1">
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-orange-600"></div>
                    <span className="text-xs text-orange-600">Processing...</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Upload Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Upload Photos</h2>
          <div className="flex items-center gap-3">
            <button
              onClick={handleStartProcessing}
              disabled={isUploading || images.length === 0}
              className="btn-secondary flex items-center gap-2"
            >
              <BarChart3 className="h-4 w-4" />
              Start Processing
            </button>
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
              className="btn-primary flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              {isUploading ? 'Uploading...' : 'Upload Photos'}
            </button>
          </div>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
          className="hidden"
        />

        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 mb-2">
            Drag and drop images here, or click "Upload Photos" to select files
          </p>
          <p className="text-sm text-gray-500">
            Supports JPG, PNG, GIF. Face recognition will be automatically processed.
          </p>
        </div>
      </div>

      {/* Images Grid */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Event Photos</h2>
          <div className="text-sm text-gray-600">
            {images.length} photos uploaded
          </div>
        </div>

        {images.length === 0 ? (
          <div className="text-center py-8">
            <ImageIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-600">No photos uploaded yet</p>
            <p className="text-sm text-gray-500">Upload some photos to get started with face recognition</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {images.map((image) => (
              <div key={image.id} className="relative group">
                <img
                  src={eventService.getImageUrl(image.path)}
                  alt={image.originalName}
                  className="w-full h-32 object-cover rounded-lg"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity rounded-lg flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <button className="text-white p-2 hover:bg-white hover:bg-opacity-20 rounded">
                      <Eye className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                
                {/* Processing Status */}
                <div className="absolute top-2 left-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    image.processingStatus === 'completed' 
                      ? 'bg-green-100 text-green-800' 
                      : image.processingStatus === 'processing'
                      ? 'bg-yellow-100 text-yellow-800'
                      : image.processingStatus === 'failed'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {image.processingStatus === 'completed' && `${image.faceCount} faces`}
                    {image.processingStatus === 'processing' && 'Processing...'}
                    {image.processingStatus === 'failed' && 'Failed'}
                    {image.processingStatus === 'pending' && 'Pending'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Event Info */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Event Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Event ID</label>
            <div className="mt-1 flex">
              <input
                type="text"
                value={event.uniqueId}
                readOnly
                className="input-field flex-1"
              />
              <button
                onClick={() => copyToClipboard(event.uniqueId, 'Event ID')}
                className="ml-2 btn-secondary px-3"
              >
                Copy
              </button>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">Access Link</label>
            <div className="mt-1 flex">
              <input
                type="text"
                value={event.accessLink}
                readOnly
                className="input-field flex-1"
              />
              <button
                onClick={() => copyToClipboard(event.accessLink, 'Access link')}
                className="ml-2 btn-secondary px-3"
              >
                Copy
              </button>
            </div>
          </div>
        </div>
        
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex justify-between text-sm text-gray-600">
            <span>Created: {new Date(event.createdAt).toLocaleString()}</span>
            <span>Last updated: {new Date(event.updatedAt).toLocaleString()}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

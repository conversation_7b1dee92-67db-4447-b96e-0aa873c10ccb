'use client';

import { useState, useEffect } from 'react';
import { Settings, Camera, User, Lock, Upload, Shield, Users, Image } from 'lucide-react';
import { authService } from '@/lib/auth';
import { settingsService } from '@/lib/services';
import { StudioProfile, StudioSettings } from '@/types';
import toast from 'react-hot-toast';
import ProfileTab from '@/components/settings/ProfileTab';
import SecurityTab from '@/components/settings/SecurityTab';
import StudioTab from '@/components/settings/StudioTab';
import ClientSettingsTab from '@/components/settings/ClientSettingsTab';
import UploadSettingsTab from '@/components/settings/UploadSettingsTab';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('profile');
  const [profile, setProfile] = useState<StudioProfile | null>(null);
  const [settings, setSettings] = useState<StudioSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const user = authService.getCurrentUser();

  const tabs = [
    { id: 'profile', name: 'Profile', icon: User },
    { id: 'security', name: 'Security', icon: Lock },
    { id: 'studio', name: 'Studio Settings', icon: Camera },
    { id: 'clients', name: 'Client Settings', icon: Users },
    { id: 'uploads', name: 'Upload Settings', icon: Upload },
  ];

  useEffect(() => {
    fetchSettingsData();
  }, []);

  const fetchSettingsData = async () => {
    try {
      const [profileData, settingsData] = await Promise.all([
        settingsService.getStudioProfile(),
        settingsService.getStudioSettings(),
      ]);
      setProfile(profileData);
      setSettings(settingsData);
    } catch (error) {
      toast.error('Failed to fetch settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleProfileUpdate = (updatedProfile: StudioProfile) => {
    setProfile(updatedProfile);
  };

  const handleSettingsUpdate = (updatedSettings: StudioSettings) => {
    setSettings(updatedSettings);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Manage your studio settings and preferences</p>
      </div>

      <div className="flex space-x-8">
        {/* Sidebar */}
        <div className="w-64">
          <nav className="space-y-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                }`}
              >
                <tab.icon className="h-5 w-5 mr-3" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1">
          <div className="card">
            {activeTab === 'profile' && profile && (
              <ProfileTab
                profile={profile}
                onUpdate={handleProfileUpdate}
              />
            )}

            {activeTab === 'security' && settings && (
              <SecurityTab
                settings={settings}
                onUpdate={handleSettingsUpdate}
              />
            )}

            {activeTab === 'studio' && profile && settings && (
              <StudioTab
                profile={profile}
                settings={settings}
                onProfileUpdate={handleProfileUpdate}
                onSettingsUpdate={handleSettingsUpdate}
              />
            )}

            {activeTab === 'clients' && settings && (
              <ClientSettingsTab
                settings={settings}
                onUpdate={handleSettingsUpdate}
              />
            )}

            {activeTab === 'uploads' && settings && (
              <UploadSettingsTab
                settings={settings}
                onUpdate={handleSettingsUpdate}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

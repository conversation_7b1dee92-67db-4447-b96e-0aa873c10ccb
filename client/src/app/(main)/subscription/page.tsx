'use client';

import { useEffect, useState } from 'react';
import { 
  Check, 
  X, 
  Crown, 
  Zap, 
  Star, 
  Users, 
  HardDrive, 
  Camera,
  Shield,
  Palette,
  BarChart3,
  Headphones,
  Globe,
  Code
} from 'lucide-react';
import { subscriptionService } from '@/lib/services';
import { SubscriptionPlan, StudioSubscription } from '@/types';
import toast from 'react-hot-toast';

export default function SubscriptionPage() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<StudioSubscription | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [plansData, subscriptionData] = await Promise.all([
        subscriptionService.getAllPlans(),
        subscriptionService.getMySubscription(),
      ]);
      setPlans(plansData);
      setCurrentSubscription(subscriptionData);
    } catch (error) {
      toast.error('Failed to fetch subscription data');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePlanSelect = async (planId: string) => {
    if (isUpdating) return;
    
    setIsUpdating(true);
    setSelectedPlan(planId);

    try {
      if (currentSubscription) {
        await subscriptionService.updateSubscription(planId);
        toast.success('Subscription updated successfully!');
      } else {
        await subscriptionService.createSubscription(planId);
        toast.success('Subscription created successfully!');
      }
      
      await fetchData();
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to update subscription');
    } finally {
      setIsUpdating(false);
      setSelectedPlan(null);
    }
  };

  const getPlanIcon = (type: string) => {
    switch (type) {
      case 'FREE':
        return <Star className="h-8 w-8" />;
      case 'BASIC':
        return <Zap className="h-8 w-8" />;
      case 'PREMIUM':
        return <Crown className="h-8 w-8" />;
      case 'ENTERPRISE':
        return <Shield className="h-8 w-8" />;
      default:
        return <Star className="h-8 w-8" />;
    }
  };

  const getPlanColor = (type: string) => {
    switch (type) {
      case 'FREE':
        return 'bg-gray-100 text-gray-600';
      case 'BASIC':
        return 'bg-blue-100 text-blue-600';
      case 'PREMIUM':
        return 'bg-purple-100 text-purple-600';
      case 'ENTERPRISE':
        return 'bg-gold-100 text-gold-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  const isCurrentPlan = (planId: string) => {
    return currentSubscription?.planId === planId;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900">Choose Your Plan</h1>
        <p className="text-gray-600 mt-2">Select the perfect plan for your photography studio</p>
        
        {currentSubscription && (
          <div className="mt-4 p-4 bg-blue-50 rounded-lg inline-block">
            <p className="text-sm text-blue-800">
              Current Plan: <span className="font-semibold">{currentSubscription.plan.name}</span>
              {currentSubscription.status === 'TRIAL' && (
                <span className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                  Trial
                </span>
              )}
            </p>
          </div>
        )}
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {plans.map((plan) => (
          <div
            key={plan.id}
            className={`relative rounded-2xl border-2 p-8 shadow-lg transition-all duration-300 hover:shadow-xl ${
              isCurrentPlan(plan.id)
                ? 'border-primary-500 bg-primary-50'
                : 'border-gray-200 bg-white hover:border-primary-300'
            } ${plan.type === 'PREMIUM' ? 'ring-2 ring-purple-500 ring-opacity-50' : ''}`}
          >
            {plan.type === 'PREMIUM' && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-purple-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                  Most Popular
                </span>
              </div>
            )}

            {/* Plan Header */}
            <div className="text-center mb-8">
              <div className={`inline-flex p-3 rounded-full ${getPlanColor(plan.type)} mb-4`}>
                {getPlanIcon(plan.type)}
              </div>
              <h3 className="text-xl font-bold text-gray-900">{plan.name}</h3>
              <div className="mt-4">
                <span className="text-4xl font-bold text-gray-900">${plan.price}</span>
                <span className="text-gray-600">/{plan.billingCycle}</span>
              </div>
              {plan.trialDays > 0 && (
                <p className="text-sm text-green-600 mt-2">{plan.trialDays} days free trial</p>
              )}
            </div>

            {/* Features */}
            <div className="space-y-4 mb-8">
              <div className="flex items-center">
                <Users className="h-5 w-5 text-gray-400 mr-3" />
                <span className="text-sm text-gray-600">
                  {plan.maxClients === 0 ? 'Unlimited' : plan.maxClients} clients
                </span>
              </div>
              
              <div className="flex items-center">
                <Users className="h-5 w-5 text-gray-400 mr-3" />
                <span className="text-sm text-gray-600">
                  {plan.maxUsers === 0 ? 'Unlimited' : plan.maxUsers} team members
                </span>
              </div>
              
              <div className="flex items-center">
                <HardDrive className="h-5 w-5 text-gray-400 mr-3" />
                <span className="text-sm text-gray-600">{plan.maxStorageGB}GB storage</span>
              </div>
              
              <div className="flex items-center">
                <Camera className="h-5 w-5 text-gray-400 mr-3" />
                <span className="text-sm text-gray-600">
                  {plan.maxImagesPerClient === 0 ? 'Unlimited' : plan.maxImagesPerClient} images per client
                </span>
              </div>

              {/* Advanced Features */}
              <div className="pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Palette className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-xs text-gray-600">Custom Branding</span>
                  </div>
                  {plan.customBranding ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-gray-300" />
                  )}
                </div>
                
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center">
                    <BarChart3 className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-xs text-gray-600">Advanced Analytics</span>
                  </div>
                  {plan.advancedAnalytics ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-gray-300" />
                  )}
                </div>
                
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center">
                    <Headphones className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-xs text-gray-600">Priority Support</span>
                  </div>
                  {plan.prioritySupport ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-gray-300" />
                  )}
                </div>
                
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center">
                    <Code className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-xs text-gray-600">API Access</span>
                  </div>
                  {plan.apiAccess ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-gray-300" />
                  )}
                </div>
                
                {plan.type === 'ENTERPRISE' && (
                  <>
                    <div className="flex items-center justify-between mt-2">
                      <div className="flex items-center">
                        <Globe className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-xs text-gray-600">White Label</span>
                      </div>
                      <Check className="h-4 w-4 text-green-500" />
                    </div>
                    
                    <div className="flex items-center justify-between mt-2">
                      <div className="flex items-center">
                        <Globe className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-xs text-gray-600">Custom Domain</span>
                      </div>
                      <Check className="h-4 w-4 text-green-500" />
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Action Button */}
            <button
              onClick={() => handlePlanSelect(plan.id)}
              disabled={isUpdating || isCurrentPlan(plan.id)}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                isCurrentPlan(plan.id)
                  ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                  : selectedPlan === plan.id
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : plan.type === 'PREMIUM'
                  ? 'bg-purple-600 text-white hover:bg-purple-700'
                  : 'bg-primary-600 text-white hover:bg-primary-700'
              }`}
            >
              {isCurrentPlan(plan.id)
                ? 'Current Plan'
                : selectedPlan === plan.id
                ? 'Updating...'
                : currentSubscription
                ? 'Switch to This Plan'
                : 'Get Started'
              }
            </button>
          </div>
        ))}
      </div>

      {/* FAQ or Additional Info */}
      <div className="text-center text-sm text-gray-500">
        <p>All plans include basic features like client galleries, image uploads, and secure storage.</p>
        <p className="mt-1">Need a custom plan? <a href="mailto:<EMAIL>" className="text-primary-600 hover:underline">Contact us</a></p>
      </div>
    </div>
  );
}

import { User, LoginRequest, RegisterStudioRequest, AuthResponse, ApiResponse } from '@/types';
import api from './api';

export const authService = {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await api.post<ApiResponse<AuthResponse>>('/users/studio/login', credentials);
    
    if (response.data.success && response.data.data) {
      const { token, user } = response.data.data;
      localStorage.setItem('studio_token', token);
      localStorage.setItem('studio_user', JSON.stringify(user));
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Login failed');
  },

  async register(data: RegisterStudioRequest): Promise<any> {
    const response = await api.post<ApiResponse>('/users/studio/register', data);
    
    if (response.data.success) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Registration failed');
  },

  logout() {
    localStorage.removeItem('studio_token');
    localStorage.removeItem('studio_user');
    window.location.href = '/login';
  },

  getCurrentUser(): User | null {
    if (typeof window === 'undefined') return null;
    
    const userStr = localStorage.getItem('studio_user');
    if (!userStr) return null;
    
    try {
      return JSON.parse(userStr);
    } catch {
      return null;
    }
  },

  getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('studio_token');
  },

  isAuthenticated(): boolean {
    return !!this.getToken() && !!this.getCurrentUser();
  },
};

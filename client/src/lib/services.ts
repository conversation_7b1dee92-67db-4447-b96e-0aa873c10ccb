import {
  User,
  Client,
  Event,
  EventImage,
  EventStats,
  CreateEventRequest,
  UpdateEventRequest,
  Image,
  DashboardStats,
  RecentActivity,
  StudioProfile,
  StudioSettings,
  CreateClientRequest,
  UpdateClientRequest,
  SubscriptionPlan,
  StudioSubscription,
  ApiResponse
} from '@/types';
import api from './api';

export const userService = {
  async getStudioUsers(): Promise<User[]> {
    const response = await api.get<ApiResponse<User[]>>('/users/studio/users');
    return response.data.data || [];
  },
};

export const eventService = {
  // Event management
  async createEvent(data: CreateEventRequest): Promise<Event> {
    const response = await api.post<ApiResponse<Event>>('/events', data);
    return response.data.data!;
  },

  async getStudioEvents(): Promise<Event[]> {
    const response = await api.get<ApiResponse<Event[]>>('/events');
    return response.data.data || [];
  },

  async getEventById(eventId: string): Promise<Event> {
    const response = await api.get<ApiResponse<Event>>(`/events/${eventId}`);
    return response.data.data!;
  },

  async updateEvent(eventId: string, data: UpdateEventRequest): Promise<Event> {
    const response = await api.put<ApiResponse<Event>>(`/events/${eventId}`, data);
    return response.data.data!;
  },

  async deleteEvent(eventId: string): Promise<void> {
    await api.delete(`/events/${eventId}`);
  },

  async regenerateQRCode(eventId: string): Promise<{ qrCode: string }> {
    const response = await api.post<ApiResponse<{ qrCode: string }>>(`/events/${eventId}/regenerate-qr`);
    return response.data.data!;
  },

  async getEventStats(eventId: string): Promise<EventStats> {
    const response = await api.get<ApiResponse<EventStats>>(`/events/${eventId}/stats`);
    return response.data.data!;
  },

  // Image management for events
  async uploadEventImages(eventId: string, files: File[], descriptions?: string[]): Promise<EventImage[]> {
    const formData = new FormData();
    formData.append('eventId', eventId);

    files.forEach((file) => {
      formData.append('images', file);
    });

    if (descriptions) {
      formData.append('descriptions', JSON.stringify(descriptions));
    }

    const response = await api.post<ApiResponse<EventImage[]>>(
      `/events/${eventId}/images/bulk-upload`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 300000, // 5 minutes for bulk upload
      }
    );
    return response.data.data || [];
  },

  async getEventImages(eventId: string): Promise<EventImage[]> {
    const response = await api.get<ApiResponse<EventImage[]>>(`/events/${eventId}/images`);
    return response.data.data || [];
  },

  async deleteEventImage(imageId: string): Promise<void> {
    await api.delete(`/events/images/${imageId}`);
  },

  getImageUrl(imagePath: string): string {
    return `${process.env.NEXT_PUBLIC_API_URL?.replace('/api', '') || 'http://localhost:5000'}/${imagePath}`;
  },
};

export const clientService = {
  // Client management
  async createClient(data: CreateClientRequest): Promise<Client> {
    const response = await api.post<ApiResponse<Client>>('/clients', data);
    return response.data.data!;
  },

  async getStudioClients(): Promise<Client[]> {
    const response = await api.get<ApiResponse<Client[]>>('/clients');
    return response.data.data || [];
  },

  async getClientById(clientId: string): Promise<Client> {
    const response = await api.get<ApiResponse<Client>>(`/clients/${clientId}`);
    return response.data.data!;
  },

  async updateClient(clientId: string, data: UpdateClientRequest): Promise<Client> {
    const response = await api.put<ApiResponse<Client>>(`/clients/${clientId}`, data);
    return response.data.data!;
  },

  async deleteClient(clientId: string): Promise<void> {
    await api.delete(`/clients/${clientId}`);
  },

  async resetClientPassword(clientId: string): Promise<{ newPassword: string }> {
    const response = await api.post<ApiResponse<{ newPassword: string }>>(`/clients/${clientId}/reset-password`);
    return response.data.data!;
  },

  async regenerateQRCode(clientId: string): Promise<Client> {
    const response = await api.post<ApiResponse<Client>>(`/clients/${clientId}/regenerate-qr`);
    return response.data.data!;
  },

  // Image management
  async bulkUploadImages(clientId: string, files: File[], descriptions?: string[]): Promise<Image[]> {
    const formData = new FormData();
    formData.append('clientId', clientId);

    files.forEach((file) => {
      formData.append('images', file);
    });

    if (descriptions) {
      descriptions.forEach((desc, index) => {
        formData.append(`descriptions[${index}]`, desc);
      });
    }

    const response = await api.post<ApiResponse<Image[]>>('/clients/images/bulk-upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.data || [];
  },

  async getClientImages(clientId: string): Promise<Image[]> {
    const response = await api.get<ApiResponse<Image[]>>(`/clients/${clientId}/images`);
    return response.data.data || [];
  },

  async updateImage(imageId: string, data: { description?: string; tags?: string }): Promise<Image> {
    const response = await api.put<ApiResponse<Image>>(`/clients/images/${imageId}`, data);
    return response.data.data!;
  },

  async deleteImage(imageId: string): Promise<void> {
    await api.delete(`/clients/images/${imageId}`);
  },
};

export const settingsService = {
  // Dashboard
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await api.get<ApiResponse<DashboardStats>>('/settings/dashboard-stats');
    return response.data.data!;
  },

  async getRecentActivity(): Promise<RecentActivity> {
    const response = await api.get<ApiResponse<RecentActivity>>('/settings/recent-activity');
    return response.data.data!;
  },

  // Profile management
  async getStudioProfile(): Promise<StudioProfile> {
    const response = await api.get<ApiResponse<StudioProfile>>('/settings/profile');
    return response.data.data!;
  },

  async updateStudioProfile(data: Partial<StudioProfile>): Promise<StudioProfile> {
    const response = await api.put<ApiResponse<StudioProfile>>('/settings/profile', data);
    return response.data.data!;
  },

  // Settings management
  async getStudioSettings(): Promise<StudioSettings> {
    const response = await api.get<ApiResponse<StudioSettings>>('/settings/studio-settings');
    return response.data.data!;
  },

  async updateProfileSettings(data: Partial<StudioSettings>): Promise<StudioSettings> {
    const response = await api.put<ApiResponse<StudioSettings>>('/settings/profile-settings', data);
    return response.data.data!;
  },

  async updateSecuritySettings(data: Partial<StudioSettings>): Promise<StudioSettings> {
    const response = await api.put<ApiResponse<StudioSettings>>('/settings/security-settings', data);
    return response.data.data!;
  },

  async updateClientSettings(data: Partial<StudioSettings>): Promise<StudioSettings> {
    const response = await api.put<ApiResponse<StudioSettings>>('/settings/client-settings', data);
    return response.data.data!;
  },

  async updateUploadSettings(data: Partial<StudioSettings>): Promise<StudioSettings> {
    const response = await api.put<ApiResponse<StudioSettings>>('/settings/upload-settings', data);
    return response.data.data!;
  },

  // Password management
  async changePassword(currentPassword: string, newPassword: string, confirmPassword: string): Promise<void> {
    await api.post('/settings/change-password', {
      currentPassword,
      newPassword,
      confirmPassword,
    });
  },

  // File uploads
  async uploadProfilePicture(file: File): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('profilePicture', file);

    const response = await api.post<ApiResponse<{ url: string }>>('/settings/upload-profile-picture', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.data!;
  },

  async uploadLogo(file: File): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('logo', file);

    const response = await api.post<ApiResponse<{ url: string }>>('/settings/upload-logo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.data!;
  },
};

export const subscriptionService = {
  // Get all available plans
  async getAllPlans(): Promise<SubscriptionPlan[]> {
    const response = await api.get<ApiResponse<SubscriptionPlan[]>>('/subscriptions/plans');
    return response.data.data || [];
  },

  // Get current studio subscription
  async getMySubscription(): Promise<StudioSubscription | null> {
    try {
      const response = await api.get<ApiResponse<StudioSubscription>>('/subscriptions/my-subscription');
      return response.data.data || null;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  },

  // Create new subscription
  async createSubscription(planId: string): Promise<StudioSubscription> {
    const response = await api.post<ApiResponse<StudioSubscription>>('/subscriptions/subscribe', { planId });
    return response.data.data!;
  },

  // Update subscription plan
  async updateSubscription(planId: string): Promise<StudioSubscription> {
    const response = await api.put<ApiResponse<StudioSubscription>>('/subscriptions/update-plan', { planId });
    return response.data.data!;
  },

  // Cancel subscription
  async cancelSubscription(): Promise<StudioSubscription> {
    const response = await api.post<ApiResponse<StudioSubscription>>('/subscriptions/cancel');
    return response.data.data!;
  },
};

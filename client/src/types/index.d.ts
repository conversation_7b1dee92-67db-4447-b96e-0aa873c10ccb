export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  role: 'ADMIN' | 'STUDIO' | 'USER';
  createdAt: string;
}

export interface Studio {
  id: string;
  email: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  createdAt: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  type: 'FREE' | 'BASIC' | 'PREMIUM' | 'ENTERPRISE';
  price: number;
  currency: string;
  maxClients: number;
  maxUsers: number;
  maxStorageGB: number;
  maxImagesPerClient: number;
  customBranding: boolean;
  advancedAnalytics: boolean;
  prioritySupport: boolean;
  apiAccess: boolean;
  whiteLabel: boolean;
  customDomain: boolean;
  billingCycle: string;
  trialDays: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface StudioSubscription {
  id: string;
  studioId: string;
  planId: string;
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'TRIAL';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  trialEnd?: string;
  cancelledAt?: string;
  usedClients: number;
  usedUsers: number;
  usedStorageGB: number;
  createdAt: string;
  updatedAt: string;
  plan: SubscriptionPlan;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterStudioRequest {
  email: string;
  password: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface Event {
  id: string;
  studioId: string;
  name: string;
  description?: string;
  isPublic: boolean;
  uniqueId: string;
  accessLink: string;
  qrCode?: string;
  createdAt: string;
  updatedAt: string;
  _count?: {
    images: number;
    visitors: number;
  };
  images?: EventImage[];
  visitors?: EventVisitor[];
}

export interface EventImage {
  id: string;
  eventId: string;
  filename: string;
  originalName: string;
  path: string;
  size: number;
  mimeType: string;
  faceCount: number;
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
  uploadedAt: string;
  faceDescriptors?: FaceDescriptor[];
}

export interface FaceDescriptor {
  id: string;
  imageId: string;
  descriptorVector: number[];
  faceLocation?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  confidence: number;
  faceLandmarks?: any;
  createdAt: string;
}

export interface EventVisitor {
  id: string;
  eventId: string;
  name: string;
  contact?: string;
  email?: string;
  selfiePath: string;
  selfieDescriptor: number[];
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
  visitedAt: string;
  matches?: VisitorMatch[];
}

export interface VisitorMatch {
  id: string;
  visitorId: string;
  imageId: string;
  faceDescriptorId: string;
  confidenceScore: number;
  matchThreshold: number;
  matchedAt: string;
  image?: EventImage;
}

export interface CreateEventRequest {
  name: string;
  description?: string;
  isPublic?: boolean;
}

export interface UpdateEventRequest {
  name?: string;
  description?: string;
  isPublic?: boolean;
}

export interface EventStats {
  eventId: string;
  totalImages: number;
  totalVisitors: number;
  totalFaces: number;
  pythonStats?: any;
}

export interface Client {
  id: string;
  uniqueId: string;
  name: string;
  email?: string;
  phone?: string;
  qrCode?: string;
  accessLink: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  _count?: {
    images: number;
  };
}

export interface CreateClientRequest {
  name: string;
  email?: string;
  phone?: string;
}

export interface UpdateClientRequest {
  name?: string;
  email?: string;
  phone?: string;
  isActive?: boolean;
}

export interface Image {
  id: string;
  filename: string;
  originalName: string;
  path: string;
  size: number;
  mimeType: string;
  description?: string;
  tags?: string;
  uploadedAt: string;
  client?: {
    name: string;
    uniqueId: string;
  };
}

export interface DashboardStats {
  totalClients: number;
  activeClients: number;
  totalImages: number;
  recentClients: number;
  recentImages: number;
  storageUsed: number;
}

export interface RecentActivity {
  recentClients: Client[];
  recentImages: Image[];
}

export interface StudioSettings {
  id: string;
  studioId: string;
  profilePicture?: string;
  businessAddress?: string;
  operatingHours?: string;
  socialLinks?: any;
  logoUrl?: string;
  twoFactorEnabled: boolean;
  sessionTimeout: number;
  defaultAccessDuration: number;
  autoGeneratePassword: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  maxFileSize: number;
  allowedFormats: string;
  autoResize: boolean;
  storageQuota: number;
}

export interface StudioProfile {
  id: string;
  email: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
  settings: StudioSettings;
}

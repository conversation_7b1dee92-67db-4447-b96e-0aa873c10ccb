# Photo Cap Client Panel

Next.js 14 client panel for studios to manage their users and settings in the Photo Cap application.

## 🚀 Setup

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.local.example .env.local
# Edit .env.local with your API URL
```

3. Start development server:
```bash
npm run dev
```

The client panel will be available at `http://localhost:3001`

## 🔐 Demo Studio Credentials

- **Email**: <EMAIL>
- **Password**: studio123

## 📋 Features

- **Studio Registration** - Register new photography studios
- **Studio Login** - Secure authentication for studios
- **Dashboard** - Overview of studio statistics and users
- **User Management** - View users associated with the studio
- **Settings** - Manage studio profile and preferences
- **Responsive Design** - Works on desktop and mobile devices

## 🎨 Tech Stack

- **Next.js 14** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **React Hook Form** for form handling
- **Axios** for API calls
- **React Hot Toast** for notifications
- **Lucide React** for icons

## 📁 Project Structure

```
src/
├── app/
│   ├── (auth)/          # Authentication pages
│   │   ├── login/
│   │   └── register/
│   └── (main)/          # Protected pages
│       ├── dashboard/
│       ├── users/
│       └── settings/
├── components/          # Reusable components
├── lib/                 # Utilities and services
└── types/              # TypeScript type definitions
```

## 🛠️ Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## 🌐 Environment Variables

```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api
```

## 📝 Studio Registration Process

1. Studio registers through the registration form
2. Registration is submitted with status "PENDING"
3. Admin reviews and approves/rejects the studio
4. Once approved, studio can login and access the dashboard

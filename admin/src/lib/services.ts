import {
  Studio,
  User,
  ApiResponse,
  CreateStudioRequest,
  UpdateStudioRequest,
  SubscriptionPlan,
  StudioSubscription,
  SubscriptionAnalytics
} from '@/types';
import api from './api';

export const studioService = {
  async getAllStudios(): Promise<Studio[]> {
    const response = await api.get<ApiResponse<Studio[]>>('/users/admin/studios');
    return response.data.data || [];
  },

  async getStudioById(studioId: string): Promise<Studio> {
    const response = await api.get<ApiResponse<Studio>>(`/users/admin/studios/${studioId}`);
    return response.data.data!;
  },

  async createStudio(data: CreateStudioRequest): Promise<Studio> {
    const response = await api.post<ApiResponse<Studio>>('/users/admin/studios', data);
    return response.data.data!;
  },

  async updateStudio(studioId: string, data: UpdateStudioRequest): Promise<Studio> {
    const response = await api.put<ApiResponse<Studio>>(`/users/admin/studios/${studioId}`, data);
    return response.data.data!;
  },

  async deleteStudio(studioId: string): Promise<void> {
    await api.delete(`/users/admin/studios/${studioId}`);
  },

  async updateStudioStatus(studioId: string, status: 'APPROVED' | 'REJECTED'): Promise<Studio> {
    const response = await api.patch<ApiResponse<Studio>>(
      `/users/admin/studios/${studioId}/status`,
      { status }
    );
    return response.data.data!;
  },
};

export const userService = {
  async getAllUsers(): Promise<User[]> {
    const response = await api.get<ApiResponse<User[]>>('/users/admin/users');
    return response.data.data || [];
  },
};

export const subscriptionService = {
  // Get all subscription plans
  async getAllPlans(): Promise<SubscriptionPlan[]> {
    const response = await api.get<ApiResponse<SubscriptionPlan[]>>('/subscriptions/plans');
    return response.data.data || [];
  },

  // Get all studio subscriptions
  async getAllStudioSubscriptions(): Promise<StudioSubscription[]> {
    const response = await api.get<ApiResponse<StudioSubscription[]>>('/subscriptions/admin/subscriptions');
    return response.data.data || [];
  },

  // Get subscription analytics
  async getSubscriptionAnalytics(): Promise<SubscriptionAnalytics> {
    const response = await api.get<ApiResponse<SubscriptionAnalytics>>('/subscriptions/admin/analytics');
    return response.data.data!;
  },

  // Update studio subscription (admin)
  async updateStudioSubscription(studioId: string, planId?: string, status?: string): Promise<StudioSubscription> {
    const response = await api.put<ApiResponse<StudioSubscription>>(
      `/subscriptions/admin/subscriptions/${studioId}`,
      { planId, status }
    );
    return response.data.data!;
  },

  // Create subscription plan (admin)
  async createPlan(planData: Partial<SubscriptionPlan>): Promise<SubscriptionPlan> {
    const response = await api.post<ApiResponse<SubscriptionPlan>>('/subscriptions/admin/plans', planData);
    return response.data.data!;
  },

  // Update subscription plan (admin)
  async updatePlan(planId: string, planData: Partial<SubscriptionPlan>): Promise<SubscriptionPlan> {
    const response = await api.put<ApiResponse<SubscriptionPlan>>(`/subscriptions/admin/plans/${planId}`, planData);
    return response.data.data!;
  },

  // Delete subscription plan (admin)
  async deletePlan(planId: string): Promise<void> {
    await api.delete(`/subscriptions/admin/plans/${planId}`);
  },
};

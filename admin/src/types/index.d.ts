export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  role: 'ADMIN' | 'STUDIO' | 'USER';
  createdAt: string;
  studio?: {
    id: string;
    name: string;
  };
}

export interface Client {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  uniqueId: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  _count?: {
    images: number;
  };
}

export interface Studio {
  id: string;
  email: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  createdAt: string;
  updatedAt?: string;
  totalStorageMB?: number;
  _count?: {
    users: number;
    clients: number;
    images: number;
  };
  clients?: Client[];
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface CreateStudioRequest {
  email: string;
  password: string;
  name: string;
  description?: string;
  phone?: string;
  address?: string;
  status?: 'PENDING' | 'APPROVED' | 'REJECTED';
}

export interface UpdateStudioRequest {
  name?: string;
  email?: string;
  description?: string;
  phone?: string;
  address?: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  type: 'FREE' | 'BASIC' | 'PREMIUM' | 'ENTERPRISE';
  price: number;
  currency: string;
  maxClients: number;
  maxUsers: number;
  maxStorageGB: number;
  maxImagesPerClient: number;
  customBranding: boolean;
  advancedAnalytics: boolean;
  prioritySupport: boolean;
  apiAccess: boolean;
  whiteLabel: boolean;
  customDomain: boolean;
  billingCycle: string;
  trialDays: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface StudioSubscription {
  id: string;
  studioId: string;
  planId: string;
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'TRIAL';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  trialEnd?: string;
  cancelledAt?: string;
  usedClients: number;
  usedUsers: number;
  usedStorageGB: number;
  createdAt: string;
  updatedAt: string;
  plan: SubscriptionPlan;
  studio: {
    id: string;
    name: string;
    email: string;
    status: string;
  };
}

export interface SubscriptionAnalytics {
  totalSubscriptions: number;
  subscriptionsByPlan: Array<{
    plan: SubscriptionPlan;
    count: number;
  }>;
  subscriptionsByStatus: Array<{
    status: string;
    _count: {
      id: number;
    };
  }>;
}

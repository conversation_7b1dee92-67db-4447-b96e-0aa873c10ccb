'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { X } from 'lucide-react';
import { subscriptionService } from '@/lib/services';
import { SubscriptionPlan } from '@/types';
import toast from 'react-hot-toast';

interface EditPlanModalProps {
  plan: SubscriptionPlan;
  onClose: () => void;
  onSuccess: () => void;
}

interface EditPlanForm {
  name: string;
  type: 'FREE' | 'BASIC' | 'PREMIUM' | 'ENTERPRISE';
  price: number;
  currency: string;
  maxClients: number;
  maxUsers: number;
  maxStorageGB: number;
  maxImagesPerClient: number;
  customBranding: boolean;
  advancedAnalytics: boolean;
  prioritySupport: boolean;
  apiAccess: boolean;
  whiteLabel: boolean;
  customDomain: boolean;
  billingCycle: string;
  trialDays: number;
  isActive: boolean;
}

export default function EditPlanModal({ plan, onClose, onSuccess }: EditPlanModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm<EditPlanForm>();

  useEffect(() => {
    reset({
      name: plan.name,
      type: plan.type,
      price: plan.price,
      currency: plan.currency,
      maxClients: plan.maxClients,
      maxUsers: plan.maxUsers,
      maxStorageGB: plan.maxStorageGB,
      maxImagesPerClient: plan.maxImagesPerClient,
      customBranding: plan.customBranding,
      advancedAnalytics: plan.advancedAnalytics,
      prioritySupport: plan.prioritySupport,
      apiAccess: plan.apiAccess,
      whiteLabel: plan.whiteLabel,
      customDomain: plan.customDomain,
      billingCycle: plan.billingCycle,
      trialDays: plan.trialDays,
      isActive: plan.isActive,
    });
  }, [plan, reset]);

  const watchType = watch('type');

  const onSubmit = async (data: EditPlanForm) => {
    setIsLoading(true);
    try {
      // Ensure numeric values are properly converted
      const planData = {
        ...data,
        price: Number(data.price),
        maxClients: Number(data.maxClients),
        maxUsers: Number(data.maxUsers),
        maxStorageGB: Number(data.maxStorageGB),
        maxImagesPerClient: Number(data.maxImagesPerClient),
        trialDays: Number(data.trialDays),
      };

      await subscriptionService.updatePlan(plan.id, planData);
      toast.success('Plan updated successfully!');
      onSuccess();
      onClose();
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to update plan');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Edit Subscription Plan</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Plan Name *
              </label>
              <input
                type="text"
                {...register('name', { required: 'Plan name is required' })}
                className="input"
                placeholder="e.g., Professional Plan"
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Plan Type *
              </label>
              <select
                {...register('type', { required: 'Plan type is required' })}
                className="input"
              >
                <option value="">Select Type</option>
                <option value="FREE">Free</option>
                <option value="BASIC">Basic</option>
                <option value="PREMIUM">Premium</option>
                <option value="ENTERPRISE">Enterprise</option>
              </select>
              {errors.type && (
                <p className="text-red-500 text-sm mt-1">{errors.type.message}</p>
              )}
            </div>
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price *
              </label>
              <input
                type="number"
                step="0.01"
                {...register('price', {
                  required: 'Price is required',
                  min: { value: 0, message: 'Price must be 0 or greater' },
                  valueAsNumber: true
                })}
                className="input"
                placeholder="0.00"
              />
              {errors.price && (
                <p className="text-red-500 text-sm mt-1">{errors.price.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Currency
              </label>
              <select {...register('currency')} className="input">
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Billing Cycle
              </label>
              <select {...register('billingCycle')} className="input">
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>
          </div>

          {/* Limits */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Clients (0 = unlimited)
              </label>
              <input
                type="number"
                {...register('maxClients', {
                  min: { value: 0, message: 'Must be 0 or greater' },
                  valueAsNumber: true
                })}
                className="input"
                placeholder="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Users (0 = unlimited)
              </label>
              <input
                type="number"
                {...register('maxUsers', {
                  min: { value: 0, message: 'Must be 0 or greater' },
                  valueAsNumber: true
                })}
                className="input"
                placeholder="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Storage (GB)
              </label>
              <input
                type="number"
                {...register('maxStorageGB', {
                  min: { value: 1, message: 'Must be at least 1GB' },
                  valueAsNumber: true
                })}
                className="input"
                placeholder="10"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Images per Client (0 = unlimited)
              </label>
              <input
                type="number"
                {...register('maxImagesPerClient', {
                  min: { value: 0, message: 'Must be 0 or greater' },
                  valueAsNumber: true
                })}
                className="input"
                placeholder="0"
              />
            </div>
          </div>

          {/* Features */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Features
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('customBranding')}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">Custom Branding</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('advancedAnalytics')}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">Advanced Analytics</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('prioritySupport')}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">Priority Support</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('apiAccess')}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">API Access</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('whiteLabel')}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">White Label</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('customDomain')}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">Custom Domain</span>
              </label>
            </div>
          </div>

          {/* Trial and Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trial Days
              </label>
              <input
                type="number"
                {...register('trialDays', {
                  min: { value: 0, message: 'Must be 0 or greater' },
                  valueAsNumber: true
                })}
                className="input"
                placeholder="0"
              />
            </div>

            <div className="flex items-center pt-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('isActive')}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">Plan is Active</span>
              </label>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary"
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

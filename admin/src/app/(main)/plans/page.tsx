'use client';

import { useEffect, useState } from 'react';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Star, 
  Zap, 
  Crown, 
  Shield,
  Check,
  X,
  Users,
  HardDrive,
  Camera,
  Palette,
  BarChart3,
  Headphones,
  Code,
  Globe
} from 'lucide-react';
import { subscriptionService } from '@/lib/services';
import { SubscriptionPlan } from '@/types';
import CreatePlanModal from '@/components/CreatePlanModal';
import EditPlanModal from '@/components/EditPlanModal';
import toast from 'react-hot-toast';

export default function PlansPage() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      const data = await subscriptionService.getAllPlans();
      setPlans(data);
    } catch (error) {
      console.error('Error fetching plans:', error);
      toast.error('Failed to fetch plans');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreatePlan = () => {
    setShowCreateModal(true);
  };

  const handleEditPlan = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setShowEditModal(true);
  };

  const handleDeletePlan = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!selectedPlan) return;

    try {
      await subscriptionService.deletePlan(selectedPlan.id);
      toast.success('Plan deleted successfully');
      fetchPlans();
      setShowDeleteModal(false);
      setSelectedPlan(null);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to delete plan');
    }
  };

  const getPlanIcon = (type: string) => {
    switch (type) {
      case 'FREE':
        return <Star className="h-6 w-6" />;
      case 'BASIC':
        return <Zap className="h-6 w-6" />;
      case 'PREMIUM':
        return <Crown className="h-6 w-6" />;
      case 'ENTERPRISE':
        return <Shield className="h-6 w-6" />;
      default:
        return <Star className="h-6 w-6" />;
    }
  };

  const getPlanColor = (type: string) => {
    switch (type) {
      case 'FREE':
        return 'bg-gray-100 text-gray-600 border-gray-200';
      case 'BASIC':
        return 'bg-blue-100 text-blue-600 border-blue-200';
      case 'PREMIUM':
        return 'bg-purple-100 text-purple-600 border-purple-200';
      case 'ENTERPRISE':
        return 'bg-yellow-100 text-yellow-600 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Subscription Plans</h1>
          <p className="text-gray-600">Manage subscription plans and pricing</p>
        </div>
        <button
          onClick={handleCreatePlan}
          className="btn-primary flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Plan
        </button>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {plans.map((plan) => (
          <div
            key={plan.id}
            className={`relative rounded-xl border-2 p-6 bg-white hover:shadow-lg transition-all duration-300 ${getPlanColor(plan.type)}`}
          >
            {plan.type === 'PREMIUM' && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  Popular
                </span>
              </div>
            )}

            {/* Plan Header */}
            <div className="text-center mb-6">
              <div className="inline-flex p-3 rounded-full bg-white shadow-sm mb-3">
                {getPlanIcon(plan.type)}
              </div>
              <h3 className="text-lg font-bold text-gray-900">{plan.name}</h3>
              <div className="mt-2">
                <span className="text-3xl font-bold text-gray-900">{formatCurrency(plan.price)}</span>
                <span className="text-gray-600">/{plan.billingCycle}</span>
              </div>
              {plan.trialDays > 0 && (
                <p className="text-sm text-green-600 mt-1">{plan.trialDays} days free trial</p>
              )}
            </div>

            {/* Features */}
            <div className="space-y-3 mb-6">
              <div className="flex items-center text-sm">
                <Users className="h-4 w-4 text-gray-400 mr-2" />
                <span>{plan.maxClients === 0 ? 'Unlimited' : plan.maxClients} clients</span>
              </div>
              
              <div className="flex items-center text-sm">
                <Users className="h-4 w-4 text-gray-400 mr-2" />
                <span>{plan.maxUsers === 0 ? 'Unlimited' : plan.maxUsers} users</span>
              </div>
              
              <div className="flex items-center text-sm">
                <HardDrive className="h-4 w-4 text-gray-400 mr-2" />
                <span>{plan.maxStorageGB}GB storage</span>
              </div>
              
              <div className="flex items-center text-sm">
                <Camera className="h-4 w-4 text-gray-400 mr-2" />
                <span>
                  {plan.maxImagesPerClient === 0 ? 'Unlimited' : plan.maxImagesPerClient} images/client
                </span>
              </div>

              {/* Advanced Features */}
              <div className="pt-3 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center">
                    {plan.customBranding ? (
                      <Check className="h-3 w-3 text-green-500 mr-1" />
                    ) : (
                      <X className="h-3 w-3 text-gray-300 mr-1" />
                    )}
                    <span className={plan.customBranding ? 'text-gray-700' : 'text-gray-400'}>
                      Branding
                    </span>
                  </div>
                  
                  <div className="flex items-center">
                    {plan.advancedAnalytics ? (
                      <Check className="h-3 w-3 text-green-500 mr-1" />
                    ) : (
                      <X className="h-3 w-3 text-gray-300 mr-1" />
                    )}
                    <span className={plan.advancedAnalytics ? 'text-gray-700' : 'text-gray-400'}>
                      Analytics
                    </span>
                  </div>
                  
                  <div className="flex items-center">
                    {plan.prioritySupport ? (
                      <Check className="h-3 w-3 text-green-500 mr-1" />
                    ) : (
                      <X className="h-3 w-3 text-gray-300 mr-1" />
                    )}
                    <span className={plan.prioritySupport ? 'text-gray-700' : 'text-gray-400'}>
                      Support
                    </span>
                  </div>
                  
                  <div className="flex items-center">
                    {plan.apiAccess ? (
                      <Check className="h-3 w-3 text-green-500 mr-1" />
                    ) : (
                      <X className="h-3 w-3 text-gray-300 mr-1" />
                    )}
                    <span className={plan.apiAccess ? 'text-gray-700' : 'text-gray-400'}>
                      API
                    </span>
                  </div>
                  
                  {plan.type === 'ENTERPRISE' && (
                    <>
                      <div className="flex items-center">
                        <Check className="h-3 w-3 text-green-500 mr-1" />
                        <span className="text-gray-700">White Label</span>
                      </div>
                      
                      <div className="flex items-center">
                        <Check className="h-3 w-3 text-green-500 mr-1" />
                        <span className="text-gray-700">Custom Domain</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Status */}
            <div className="mb-4">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                plan.isActive 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {plan.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <button
                onClick={() => handleEditPlan(plan)}
                className="flex items-center text-sm text-blue-600 hover:text-blue-800"
              >
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </button>
              
              <button
                onClick={() => handleDeletePlan(plan)}
                className="flex items-center text-sm text-red-600 hover:text-red-800"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>

      {plans.length === 0 && (
        <div className="text-center py-12">
          <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">No subscription plans found</p>
          <button
            onClick={handleCreatePlan}
            className="btn-primary"
          >
            Create Your First Plan
          </button>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedPlan && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete Plan</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete the "{selectedPlan.name}" plan? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedPlan(null);
                }}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Plan Modal */}
      {showCreateModal && (
        <CreatePlanModal
          onClose={() => setShowCreateModal(false)}
          onSuccess={fetchPlans}
        />
      )}

      {/* Edit Plan Modal */}
      {showEditModal && selectedPlan && (
        <EditPlanModal
          plan={selectedPlan}
          onClose={() => {
            setShowEditModal(false);
            setSelectedPlan(null);
          }}
          onSuccess={fetchPlans}
        />
      )}
    </div>
  );
}

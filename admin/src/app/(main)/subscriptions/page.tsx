'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  CreditCard,
  Building2,
  Users,
  Calendar,
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock,
  Edit,
  Trash2,
  Plus,
  Star,
  ExternalLink
} from 'lucide-react';
import { subscriptionService } from '@/lib/services';
import { StudioSubscription, SubscriptionPlan, SubscriptionAnalytics } from '@/types';
import toast from 'react-hot-toast';

export default function SubscriptionsPage() {
  const router = useRouter();
  const [subscriptions, setSubscriptions] = useState<StudioSubscription[]>([]);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [analytics, setAnalytics] = useState<SubscriptionAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'ALL' | 'ACTIVE' | 'TRIAL' | 'EXPIRED' | 'CANCELLED'>('ALL');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [subscriptionsData, plansData, analyticsData] = await Promise.all([
        subscriptionService.getAllStudioSubscriptions(),
        subscriptionService.getAllPlans(),
        subscriptionService.getSubscriptionAnalytics(),
      ]);
      
      setSubscriptions(subscriptionsData);
      setPlans(plansData);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error fetching subscription data:', error);
      toast.error('Failed to fetch subscription data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateSubscription = async (studioId: string, planId: string) => {
    try {
      await subscriptionService.updateStudioSubscription(studioId, planId);
      toast.success('Subscription updated successfully');
      fetchData();
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to update subscription');
    }
  };

  const handleCancelSubscription = async (studioId: string) => {
    try {
      await subscriptionService.updateStudioSubscription(studioId, undefined, 'CANCELLED');
      toast.success('Subscription cancelled successfully');
      fetchData();
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to cancel subscription');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'TRIAL':
        return 'bg-blue-100 text-blue-800';
      case 'EXPIRED':
        return 'bg-red-100 text-red-800';
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4" />;
      case 'TRIAL':
        return <Clock className="h-4 w-4" />;
      case 'EXPIRED':
        return <XCircle className="h-4 w-4" />;
      case 'CANCELLED':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const filteredSubscriptions = subscriptions.filter(subscription => {
    if (filter === 'ALL') return true;
    return subscription.status === filter;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Subscription Management</h1>
          <p className="text-gray-600">Manage studio subscriptions and plans</p>
        </div>
        <button
          onClick={() => router.push('/plans')}
          className="btn-primary flex items-center"
        >
          <Star className="h-4 w-4 mr-2" />
          Manage Plans
        </button>
      </div>

      {/* Quick Plan Overview */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Available Plans</h3>
          <button
            onClick={() => router.push('/plans')}
            className="text-primary-600 hover:text-primary-800 flex items-center text-sm"
          >
            View All Plans
            <ExternalLink className="h-4 w-4 ml-1" />
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {plans.slice(0, 4).map((plan) => (
            <div key={plan.id} className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{plan.name}</h4>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  plan.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {plan.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
              <p className="text-2xl font-bold text-gray-900">
                ${plan.price}
                <span className="text-sm font-normal text-gray-500">/{plan.billingCycle}</span>
              </p>
              <p className="text-sm text-gray-600 mt-1">
                {analytics?.subscriptionsByPlan.find(p => p.plan?.id === plan.id)?.count || 0} studios
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Analytics Cards */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card p-6">
            <div className="flex items-center">
              <div className="bg-blue-100 p-3 rounded-full">
                <CreditCard className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Subscriptions</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.totalSubscriptions}</p>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="bg-green-100 p-3 rounded-full">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Subscriptions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.subscriptionsByStatus.find(s => s.status === 'ACTIVE')?._count.id || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="bg-purple-100 p-3 rounded-full">
                <Clock className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Trial Subscriptions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.subscriptionsByStatus.find(s => s.status === 'TRIAL')?._count.id || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="bg-red-100 p-3 rounded-full">
                <XCircle className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Expired/Cancelled</p>
                <p className="text-2xl font-bold text-gray-900">
                  {(analytics.subscriptionsByStatus.find(s => s.status === 'EXPIRED')?._count.id || 0) +
                   (analytics.subscriptionsByStatus.find(s => s.status === 'CANCELLED')?._count.id || 0)}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Plan Distribution */}
      {analytics && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Plan Distribution</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {analytics.subscriptionsByPlan.map((item, index) => (
              <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-600">{item.plan?.name || 'Unknown Plan'}</p>
                <p className="text-2xl font-bold text-gray-900">{item.count}</p>
                <p className="text-xs text-gray-500">
                  {formatCurrency(item.plan?.price || 0)}/{item.plan?.billingCycle || 'month'}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex space-x-2">
        {['ALL', 'ACTIVE', 'TRIAL', 'EXPIRED', 'CANCELLED'].map((status) => (
          <button
            key={status}
            onClick={() => setFilter(status as any)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === status
                ? 'bg-primary-600 text-white'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
          >
            {status}
          </button>
        ))}
      </div>

      {/* Subscriptions Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Studio
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plan
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usage
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Period
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredSubscriptions.map((subscription) => (
                <tr key={subscription.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="bg-primary-100 p-2 rounded-full">
                        <Building2 className="h-4 w-4 text-primary-600" />
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {subscription.studio.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {subscription.studio.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {subscription.plan.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatCurrency(subscription.plan.price)}/{subscription.plan.billingCycle}
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
                      {getStatusIcon(subscription.status)}
                      <span className="ml-1">{subscription.status}</span>
                    </span>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="space-y-1">
                      <div>{subscription.usedClients}/{subscription.plan.maxClients === 0 ? '∞' : subscription.plan.maxClients} clients</div>
                      <div>{subscription.usedUsers}/{subscription.plan.maxUsers === 0 ? '∞' : subscription.plan.maxUsers} users</div>
                      <div>{subscription.usedStorageGB.toFixed(2)}/{subscription.plan.maxStorageGB}GB storage</div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="space-y-1">
                      <div>Start: {formatDate(subscription.currentPeriodStart)}</div>
                      <div>End: {formatDate(subscription.currentPeriodEnd)}</div>
                      {subscription.trialEnd && (
                        <div className="text-blue-600">Trial: {formatDate(subscription.trialEnd)}</div>
                      )}
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <select
                        onChange={(e) => handleUpdateSubscription(subscription.studioId, e.target.value)}
                        className="text-xs border border-gray-300 rounded px-2 py-1"
                        defaultValue=""
                      >
                        <option value="" disabled>Change Plan</option>
                        {plans.map((plan) => (
                          <option key={plan.id} value={plan.id}>
                            {plan.name}
                          </option>
                        ))}
                      </select>
                      
                      {subscription.status !== 'CANCELLED' && (
                        <button
                          onClick={() => handleCancelSubscription(subscription.studioId)}
                          className="text-red-600 hover:text-red-800"
                          title="Cancel Subscription"
                        >
                          <XCircle className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredSubscriptions.length === 0 && (
          <div className="text-center py-12">
            <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No subscriptions found</p>
          </div>
        )}
      </div>
    </div>
  );
}

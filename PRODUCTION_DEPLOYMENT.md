# Production Deployment Guide

## Face Recognition System - Production Ready Setup

### 🚀 System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Studio Panel  │    │  Client Portal  │    │  Admin Panel    │
│   (Port 3001)   │    │   (Port 3002)   │    │   (Port 3000)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Node.js API    │
                    │   (Port 5000)   │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Python Face API │
                    │   (Port 8000)   │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │  PostgreSQL DB  │
                    │   (Port 5433)   │
                    └─────────────────┘
```

### 📋 Prerequisites

- Node.js 18+ 
- Python 3.8+
- PostgreSQL 12+
- Docker (optional)
- Nginx (for production)

### 🔧 Environment Configuration

#### 1. Client Portal (.env.local)
```env
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api
NEXT_PUBLIC_PYTHON_SERVICE_URL=https://face-api.yourdomain.com
NEXT_PUBLIC_APP_NAME=PhotoCap Client Portal
NEXT_PUBLIC_APP_VERSION=1.0.0
```

#### 2. Studio Panel (.env.local)
```env
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api
NEXT_PUBLIC_APP_NAME=PhotoCap Studio
NEXT_PUBLIC_APP_VERSION=1.0.0
```

#### 3. Node.js Backend (.env)
```env
NODE_ENV=production
PORT=5000
DATABASE_URL=***************************************/photocap
JWT_SECRET=your-super-secret-jwt-key
PYTHON_FACE_SERVICE_URL=https://face-api.yourdomain.com
CORS_ORIGINS=https://studio.yourdomain.com,https://client.yourdomain.com
```

#### 4. Python Face Service (.env)
```env
DATABASE_URL=***************************************/photocap
HOST=0.0.0.0
PORT=8000
DEBUG=false
SECRET_KEY=your-super-secret-python-key
FACE_RECOGNITION_TOLERANCE=0.6
UPLOAD_DIR=/app/uploads
TEMP_DIR=/app/temp
NODEJS_BACKEND_URL=https://api.yourdomain.com
ALLOWED_ORIGINS=https://studio.yourdomain.com,https://client.yourdomain.com
LOG_LEVEL=INFO
```

### 🐳 Docker Deployment

#### 1. Create docker-compose.yml
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: photocap
      POSTGRES_USER: photocap_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  backend:
    build: ./backend
    environment:
      DATABASE_URL: ********************************************************/photocap
      JWT_SECRET: your-jwt-secret
      PYTHON_FACE_SERVICE_URL: http://python-face:8000
    ports:
      - "5000:5000"
    depends_on:
      - postgres
    volumes:
      - uploads:/app/uploads

  python-face:
    build: ./python-face-service
    environment:
      DATABASE_URL: ********************************************************/photocap
      SECRET_KEY: your-python-secret
      NODEJS_BACKEND_URL: http://backend:5000
    ports:
      - "8000:8000"
    depends_on:
      - postgres
    volumes:
      - uploads:/app/uploads

  studio-panel:
    build: ./client
    environment:
      NEXT_PUBLIC_API_URL: http://backend:5000/api
    ports:
      - "3001:3000"

  client-portal:
    build: ./client-portal
    environment:
      NEXT_PUBLIC_API_URL: http://backend:5000/api
      NEXT_PUBLIC_PYTHON_SERVICE_URL: http://python-face:8000
    ports:
      - "3002:3000"

volumes:
  postgres_data:
  uploads:
```

### 🔒 Security Checklist

- [ ] Change all default passwords and secrets
- [ ] Enable HTTPS with SSL certificates
- [ ] Configure proper CORS origins
- [ ] Set up database connection pooling
- [ ] Enable rate limiting
- [ ] Configure proper file upload limits
- [ ] Set up monitoring and logging
- [ ] Enable database backups
- [ ] Configure firewall rules
- [ ] Set up error tracking (Sentry)

### 📊 Performance Optimization

#### 1. Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_events_studio_id ON events(studio_id);
CREATE INDEX idx_event_images_event_id ON event_images(event_id);
CREATE INDEX idx_face_descriptors_image_id ON face_descriptors(image_id);
CREATE INDEX idx_visitor_matches_visitor_id ON visitor_matches(visitor_id);
```

#### 2. Nginx Configuration
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # Studio Panel
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # API Backend
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        client_max_body_size 50M;
    }
    
    # Python Face Service
    location /face-api/ {
        proxy_pass http://localhost:8000/api/face/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        client_max_body_size 50M;
    }
}

# Client Portal
server {
    listen 443 ssl http2;
    server_name client.yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:3002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 🚀 Deployment Steps

1. **Prepare Server**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install dependencies
   sudo apt install -y nodejs npm python3 python3-pip postgresql nginx
   ```

2. **Clone and Setup**
   ```bash
   git clone <your-repo>
   cd photo-cap
   
   # Install dependencies
   cd backend && npm install
   cd ../client && npm install
   cd ../client-portal && npm install
   cd ../python-face-service && pip install -r requirements.txt
   ```

3. **Database Setup**
   ```bash
   # Create database
   sudo -u postgres createdb photocap
   sudo -u postgres createuser photocap_user
   
   # Run migrations
   cd backend && npx prisma migrate deploy
   cd ../python-face-service && python -m alembic upgrade head
   ```

4. **Build Applications**
   ```bash
   cd client && npm run build
   cd ../client-portal && npm run build
   ```

5. **Start Services**
   ```bash
   # Use PM2 for process management
   npm install -g pm2
   
   # Start backend
   cd backend && pm2 start npm --name "backend" -- start
   
   # Start Python service
   cd python-face-service && pm2 start "python main.py" --name "python-face"
   
   # Start frontend apps
   cd client && pm2 start npm --name "studio" -- start
   cd client-portal && pm2 start npm --name "client-portal" -- start
   ```

### 📈 Monitoring

- Set up application monitoring (PM2, New Relic)
- Configure log aggregation (ELK stack)
- Set up uptime monitoring
- Configure database monitoring
- Set up error tracking (Sentry)

### 🔄 Backup Strategy

- Daily database backups
- File upload backups
- Configuration backups
- Automated backup verification

### 🚨 Troubleshooting

Common issues and solutions:

1. **Camera not working**: Check HTTPS and permissions
2. **Face recognition failing**: Verify Python service and dependencies
3. **Database connection issues**: Check connection strings and firewall
4. **File upload issues**: Check file permissions and disk space
5. **CORS errors**: Verify allowed origins configuration

### 📞 Support

For production support:
- Check logs: `pm2 logs`
- Monitor processes: `pm2 status`
- Restart services: `pm2 restart all`
- Database status: Check PostgreSQL logs

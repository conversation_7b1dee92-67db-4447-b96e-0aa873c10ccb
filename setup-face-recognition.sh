#!/bin/bash

# Face Recognition System Setup Script
# This script sets up the complete face recognition system for Photo-Cap

set -e  # Exit on any error

echo "🚀 Setting up Face Recognition System for Photo-Cap"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "backend/package.json" ] || [ ! -f "client/package.json" ]; then
    print_error "Please run this script from the photo-cap root directory"
    exit 1
fi

print_info "Current directory: $(pwd)"

# Step 1: Update Node.js Backend Database Schema
echo ""
echo "📊 Step 1: Updating Database Schema"
echo "-----------------------------------"

cd backend

print_info "Generating Prisma client..."
npm run db:generate

print_info "Pushing database schema changes..."
npm run db:push

print_status "Database schema updated successfully"

cd ..

# Step 2: Install Python Dependencies
echo ""
echo "🐍 Step 2: Setting up Python Face Recognition Service"
echo "---------------------------------------------------"

cd python-face-service

# Check if Python 3.8+ is available
if ! command -v python3 &> /dev/null; then
    print_error "Python 3.8+ is required but not installed"
    exit 1
fi

PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
print_info "Python version: $PYTHON_VERSION"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    print_info "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
print_info "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
print_info "Upgrading pip..."
pip install --upgrade pip

# Install dependencies
print_info "Installing Python dependencies (this may take a while)..."
pip install -r requirements.txt

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    print_info "Creating .env file from template..."
    cp .env.example .env
    print_warning "Please update python-face-service/.env with your database credentials"
fi

# Create necessary directories
mkdir -p temp logs

print_status "Python service setup completed"

cd ..

# Step 3: Update Node.js Dependencies
echo ""
echo "📦 Step 3: Installing Node.js Dependencies"
echo "------------------------------------------"

# Backend dependencies
print_info "Installing backend dependencies..."
cd backend
npm install axios  # Add axios for Python service communication
print_status "Backend dependencies updated"
cd ..

# Frontend dependencies (if needed)
print_info "Checking frontend dependencies..."
cd client
npm install
print_status "Client panel dependencies checked"
cd ..

cd client-portal
npm install
print_status "Client portal dependencies checked"
cd ..

# Step 4: Environment Configuration
echo ""
echo "⚙️  Step 4: Environment Configuration"
echo "------------------------------------"

# Check backend .env
if [ ! -f "backend/.env" ]; then
    print_warning "Backend .env file not found. Please create it from .env.example"
else
    print_status "Backend .env file exists"
fi

# Add Python service URL to backend .env if not present
if ! grep -q "PYTHON_FACE_SERVICE_URL" backend/.env 2>/dev/null; then
    echo "" >> backend/.env
    echo "# Python Face Recognition Service" >> backend/.env
    echo "PYTHON_FACE_SERVICE_URL=http://localhost:8000" >> backend/.env
    print_status "Added Python service URL to backend .env"
fi

# Step 5: Database Seeding
echo ""
echo "🌱 Step 5: Database Seeding"
echo "---------------------------"

cd backend
print_info "Seeding database with initial data..."
npm run db:seed
print_status "Database seeded successfully"
cd ..

# Step 6: Create startup scripts
echo ""
echo "📝 Step 6: Creating Startup Scripts"
echo "-----------------------------------"

# Create start-all script
cat > start-all.sh << 'EOF'
#!/bin/bash

# Start all services for Face Recognition System

echo "🚀 Starting Face Recognition System"
echo "==================================="

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    kill $(jobs -p) 2>/dev/null
    exit
}

trap cleanup SIGINT SIGTERM

# Start Python Face Service
echo "Starting Python Face Recognition Service..."
cd python-face-service
source venv/bin/activate
python main.py &
PYTHON_PID=$!
cd ..

# Wait a moment for Python service to start
sleep 3

# Start Node.js Backend
echo "Starting Node.js Backend..."
cd backend
npm run dev &
BACKEND_PID=$!
cd ..

# Start Client Panel
echo "Starting Client Panel..."
cd client
npm run dev &
CLIENT_PID=$!
cd ..

# Start Client Portal
echo "Starting Client Portal..."
cd client-portal
npm run dev &
PORTAL_PID=$!
cd ..

echo ""
echo "✅ All services started!"
echo "========================"
echo "🐍 Python Face Service: http://localhost:8000"
echo "🔧 Node.js Backend: http://localhost:5000"
echo "🏢 Client Panel: http://localhost:3001"
echo "🌐 Client Portal: http://localhost:3002"
echo "📚 Python API Docs: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for all background processes
wait
EOF

chmod +x start-all.sh

# Create individual service start scripts
cat > start-python-service.sh << 'EOF'
#!/bin/bash
echo "🐍 Starting Python Face Recognition Service..."
cd python-face-service
source venv/bin/activate
python main.py
EOF

chmod +x start-python-service.sh

cat > start-backend.sh << 'EOF'
#!/bin/bash
echo "🔧 Starting Node.js Backend..."
cd backend
npm run dev
EOF

chmod +x start-backend.sh

print_status "Startup scripts created"

# Step 7: Final Instructions
echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "Next steps:"
echo "1. Update environment files with your database credentials:"
echo "   - backend/.env"
echo "   - python-face-service/.env"
echo ""
echo "2. Start all services:"
echo "   ./start-all.sh"
echo ""
echo "3. Or start services individually:"
echo "   ./start-python-service.sh    # Python Face Service (port 8000)"
echo "   ./start-backend.sh           # Node.js Backend (port 5000)"
echo "   cd client && npm run dev     # Client Panel (port 3001)"
echo "   cd client-portal && npm run dev  # Client Portal (port 3002)"
echo ""
echo "4. Access the applications:"
echo "   🏢 Studio Panel: http://localhost:3001"
echo "   🌐 Event Portal: http://localhost:3002"
echo "   📚 API Documentation: http://localhost:8000/docs"
echo ""
echo "5. Test the system:"
echo "   - Login to studio <NAME_EMAIL> / studio123"
echo "   - Create a new event"
echo "   - Upload images to the event"
echo "   - Share the event QR code or link"
echo "   - Test visitor face matching"
echo ""
print_status "Face Recognition System setup completed successfully!"
echo ""
print_warning "Remember to configure your database connection in both .env files before starting the services."

#!/usr/bin/env python3
"""
Test PhotoCap visitor registration endpoint
"""

import requests
import json

def test_photocap_endpoint():
    """Test if the PhotoCap endpoint is available"""
    print("🧪 Testing PhotoCap Visitor Registration Endpoint")
    print("=" * 50)
    
    # Test if the endpoint exists in the API docs
    try:
        response = requests.get("http://localhost:8000/openapi.json")
        if response.status_code == 200:
            openapi_spec = response.json()
            paths = openapi_spec.get("paths", {})
            
            # Check for the new PhotoCap endpoint
            photocap_endpoint = "/api/face/photocap-visitor-registration"
            fotoowl_endpoint = "/api/face/fotoowl-visitor-registration"
            
            if photocap_endpoint in paths:
                print("✅ PhotoCap endpoint found in API spec")
                print(f"   Endpoint: {photocap_endpoint}")
                return True
            elif fotoowl_endpoint in paths:
                print("❌ Still using old FotoOwl endpoint")
                print(f"   Found: {fotoowl_endpoint}")
                print("   Expected: {photocap_endpoint}")
                return False
            else:
                print("❌ Neither PhotoCap nor FotoOwl endpoint found")
                return False
        else:
            print(f"❌ Failed to get API spec: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")
        return False

def test_backend_branding():
    """Test if backend is using PhotoCap branding"""
    print("\n🏷️  Testing Backend Branding")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:5000/health")
        if response.status_code == 200:
            data = response.json()
            message = data.get("message", "")
            
            if "Photo Cap" in message:
                print("✅ Backend using PhotoCap branding")
                print(f"   Message: {message}")
                return True
            elif "FotoOwl" in message:
                print("❌ Backend still using FotoOwl branding")
                print(f"   Message: {message}")
                return False
            else:
                print("✅ Backend branding neutral")
                print(f"   Message: {message}")
                return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing backend: {e}")
        return False

def main():
    print("🎯 PhotoCap Branding & Endpoint Test")
    print("=" * 40)
    
    endpoint_ok = test_photocap_endpoint()
    branding_ok = test_backend_branding()
    
    print("\n" + "=" * 40)
    print("📊 TEST RESULTS")
    print("=" * 15)
    print(f"✅ PhotoCap Endpoint: {'PASS' if endpoint_ok else 'FAIL'}")
    print(f"✅ PhotoCap Branding: {'PASS' if branding_ok else 'FAIL'}")
    
    if endpoint_ok and branding_ok:
        print("\n🎉 SUCCESS!")
        print("PhotoCap branding implementation complete!")
        print("\n🚀 Ready for production with PhotoCap branding")
    else:
        print("\n⚠️  Some issues found:")
        if not endpoint_ok:
            print("- Update Python service endpoint from FotoOwl to PhotoCap")
        if not branding_ok:
            print("- Update backend branding to PhotoCap")
    
    return endpoint_ok and branding_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
